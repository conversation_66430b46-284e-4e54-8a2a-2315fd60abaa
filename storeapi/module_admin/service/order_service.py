from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import Dict, List, Optional, Any
import datetime
import math
import httpx
from module_admin.dao.order_dao import OrderDao
from module_admin.service.external_order_service import ExternalOrderService
from exceptions.exception import BusinessException, QueryException, ResourceNotFoundException, ValidationException, DatabaseException
from utils.log_util import logger

class OrderService:
    """订单服务"""

    @classmethod
    async def find_order_list_service(cls, query_db: AsyncSession, page: int, size: int, ) -> Dict[str, Any]:
        """
        获取订单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param order_new: 订单新旧状态
        :param product_type: 产品类型
        :param store_uuid: 门店UUID
        :return: 订单列表数据
        """
        try:
            # 调用DAO层获取订单列表
            order_list, total, order_stat = await OrderDao.find_order_list(query_db, page, size,store_uuid)

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": order_list,
                "total": total,
                "stat": order_stat,
                "order_stat_new": ""
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单列表失败: {str(e)}")

    @classmethod
    async def find_serve_order_detail_service(cls, query_db: AsyncSession, order_number: str) -> Dict[str, Any]:
        """
        获取服务订单详情服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 订单详情数据
        """
        try:
            # 调用DAO层获取订单详情
            order_detail = await OrderDao.find_serve_order_detail(query_db, order_number)

            if not order_detail:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            # 重新组织数据结构，使其更适合前端显示
            structured_detail = {
                # 基本订单信息
                "order_info": {
                    "order_number": order_detail.get("order_number", ""),
                    "order_status": order_detail.get("order_status", 0),
                    "order_status_name": cls._get_order_status_name(order_detail.get("order_status", 0)),
                    "create_time": order_detail.get("create_time", ""),
                    "update_time": order_detail.get("update_time", ""),
                    "service_time": order_detail.get("service_time", "") or cls._format_service_time(order_detail.get("service_date"), order_detail.get("service_hour")),
                    "service_date": order_detail.get("service_date", ""),
                    "service_hour": order_detail.get("service_hour", ""),
                    "store_name": order_detail.get("store_name", ""),
                    "store_uuid": order_detail.get("store_uuid", ""),
                    "total_amount": order_detail.get("total_pay_actual", "0"),
                    "preset_commission": float(order_detail.get("preset_commission", 0)) if order_detail.get("preset_commission") else 0
                },

                # 客户信息
                "customer_info": {
                    "customer_name": order_detail.get("customer_name", ""),
                    "customer_mobile": order_detail.get("customer_mobile", ""),
                    "customer_avatar": order_detail.get("customer_avatar", ""),
                    "user_id": order_detail.get("user_id", "")
                },

                # 服务地址信息
                "address_info": {
                    "service_address": order_detail.get("extend_service_address", "") or order_detail.get("service_address", ""),
                    "address_desc": order_detail.get("extend_service_address_desc", "") or order_detail.get("address_desc", ""),
                    "lng": order_detail.get("extend_service_lng", "") or order_detail.get("lng", ""),
                    "lat": order_detail.get("extend_service_lat", "") or order_detail.get("lat", ""),
                    "city": order_detail.get("city", ""),
                    "address_id": order_detail.get("address_id", "")
                },

                # 产品信息
                "product_info": {
                    "product_name": order_detail.get("product_name", ""),
                    "product_uuid": order_detail.get("product_uuid", ""),
                    "product_type": order_detail.get("product_type", 1),
                    "product_type_name": cls._get_product_type_name(order_detail.get("product_type", 1)),
                    "product_unit": order_detail.get("product_unit", ""),
                    "product_unit_name": order_detail.get("product_unit_name", ""),
                    "buy_num": order_detail.get("buy_num", "1"),
                    "service_type": order_detail.get("service_type", 1),
                    "service_type_name": cls._get_service_type_name(order_detail.get("service_type", 1))
                },

                # 支付信息
                "payment_info": {
                    "pay_money": order_detail.get("pay_money", "0"),
                    "pay_actual": order_detail.get("payment_pay_actual", "0"),
                    "pay_type": order_detail.get("payment_pay_type", ""),
                    "pay_type_name": cls._get_pay_type_name(order_detail.get("payment_pay_type", "")),
                    "pay_status": order_detail.get("pay_status", 0),
                    "pay_status_name": cls._get_pay_status_name(order_detail.get("pay_status", 0)),
                    "pay_time": order_detail.get("pay_time", ""),
                    "transaction_id": order_detail.get("transaction_id", "")
                },

                # 备注信息
                "remark_info": {
                    "service_remark": order_detail.get("service_remark", ""),
                    "after_sale_remark": order_detail.get("after_sale_remark", ""),
                    "remark": order_detail.get("remark", "")
                },

                # 服务人员信息
                "staff_info": order_detail.get("work_order", []),

                # 原始数据（保留兼容性）
                "raw_data": order_detail
            }

            return structured_detail
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单详情查询异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单详情服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单详情失败: {str(e)}")

    @classmethod
    def _get_order_status_name(cls, status) -> str:
        """获取订单状态名称"""
        # 转换为字符串进行匹配
        status_str = str(status)
        status_map = {
            '-1': '订单状态',
            '10': '已接单',
            '20': '派单待确认',
            '30': '拒绝接单',
            '40': '已派单',
            '50': '执行中',
            '60': '开始服务',
            '70': '服务结束',
            '80': '已完成',
            '90': '已评价',
            '99': '已取消',
            # 兼容旧的状态值
            '0': '待支付',
            '1': '已支付',
            '2': '派单确认',
            '3': '已派单',
            '4': '已完成',
            '5': '已取消',
            '6': '服务中',
            '7': '服务开始',
            '8': '服务结束',
            '9': '待确认'
        }
        return status_map.get(status_str, "未知状态")

    @classmethod
    def _get_product_type_name(cls, product_type: int) -> str:
        """获取产品类型名称"""
        return "单次" if product_type == 1 else "套餐"

    @classmethod
    def _get_service_type_name(cls, service_type: int) -> str:
        """获取服务类型名称"""
        return "上门" if service_type == 1 else "到店"

    @classmethod
    def _get_pay_type_name(cls, pay_type: str) -> str:
        """获取支付方式名称"""
        pay_type_map = {
            "101": "微信支付",
            "102": "支付宝",
            "106": "现金支付",
            "107": "银行转账",
            "": "未设置"
        }
        return pay_type_map.get(pay_type, "其他")

    @classmethod
    def _get_pay_status_name(cls, pay_status: int) -> str:
        """获取支付状态名称"""
        pay_status_map = {
            0: "待付款",
            1: "已支付",
            2: "已退款"
        }
        return pay_status_map.get(pay_status, "未支付")

    @classmethod
    def _format_service_time(cls, service_date, service_hour) -> str:
        """格式化服务时间"""
        if not service_date:
            return ""

        try:
            # 如果service_date是datetime对象，转换为字符串
            if hasattr(service_date, 'strftime'):
                date_str = service_date.strftime('%Y-%m-%d')
            else:
                # 如果是字符串，提取日期部分
                date_str = str(service_date).split('T')[0] if 'T' in str(service_date) else str(service_date)

            # 组合日期和时间
            if service_hour:
                return f"{date_str} {service_hour}"
            else:
                return date_str
        except Exception:
            return str(service_date) if service_date else ""

    @classmethod
    async def create_order_service(cls, query_db: AsyncSession, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建订单服务

        :param query_db: 数据库会话
        :param order_data: 订单数据
        :return: 创建的订单数据
        """
        try:
            # 验证必要字段
            if not order_data.get("address"):
                raise ValidationException(message="服务地址不能为空")

            if not order_data.get("lng") or not order_data.get("lat"):
                raise ValidationException(message="经纬度不能为空")

            if not order_data.get("city"):
                raise ValidationException(message="城市不能为空")

            if not order_data.get("user_name"):
                raise ValidationException(message="用户名不能为空")

            if not order_data.get("mobile"):
                raise ValidationException(message="手机号不能为空")

            if not order_data.get("store_uuid"):
                raise ValidationException(message="店铺唯一标识不能为空")

            if not order_data.get("product_uuid"):
                raise ValidationException(message="产品唯一标识不能为空")

            if not order_data.get("buy_num"):
                raise ValidationException(message="购买数量不能为空")

            if not order_data.get("address_id"):
                raise ValidationException(message="地址ID不能为空")

            if not order_data.get("address_user_id"):
                raise ValidationException(message="地址用户ID不能为空")

            if not order_data.get("pay_actual"):
                raise ValidationException(message="实际支付金额不能为空")

            if not order_data.get("pay_type"):
                raise ValidationException(message="支付类型不能为空")

            # 获取店铺信息
            store_query = """
                SELECT id, store_name FROM store WHERE store_uuid = :store_uuid LIMIT 1
            """
            store_result = await query_db.execute(text(store_query), {"store_uuid": order_data.get("store_uuid")})
            store_row = store_result.fetchone()

            if not store_row:
                raise ValidationException(message=f"未找到店铺: {order_data.get('store_uuid')}")

            # 获取产品信息
            product_query = """
                SELECT id, product_name, type FROM product WHERE uuid = :product_uuid LIMIT 1
            """
            product_result = await query_db.execute(text(product_query), {"product_uuid": order_data.get("product_uuid")})
            product_row = product_result.fetchone()

            if not product_row:
                raise ValidationException(message=f"未找到产品: {order_data.get('product_uuid')}")

            # 添加店铺和产品信息
            order_data["store_id"] = store_row._mapping.get("id")
            order_data["store_name"] = store_row._mapping.get("store_name")
            order_data["product_id"] = product_row._mapping.get("id")
            order_data["product_name"] = product_row._mapping.get("product_name")
            order_data["product_type"] = product_row._mapping.get("type")

            # 调用DAO层创建订单
            created_order = await OrderDao.create_order(query_db, order_data)

            return created_order
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"创建订单数据验证异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"创建订单服务异常: {str(e)}")
            raise BusinessException(message=f"创建订单失败: {str(e)}")

    @classmethod
    async def update_serve_order_service(cls, query_db: AsyncSession, order_number: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新服务订单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param update_data: 更新数据
        :return: 更新结果
        """
        try:
            # 验证订单号
            if not order_number:
                raise ValidationException(message="订单号不能为空")

            # 获取完整的订单信息，包括客户信息和服务地址
            order = await OrderDao.find_serve_order_detail(query_db, order_number)
            if not order:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            # 调用DAO层更新服务订单
            result = await OrderDao.update_serve_order(query_db, order_number, update_data)

            # 如果需要发送短信通知
            if update_data.get("send_sms") == "1" and update_data.get("service_personal"):
                try:
                    # 获取服务人员手机号
                    staff_query = """
                        SELECT user_mobile FROM service_staff
                        WHERE user_uuid = :user_uuid
                    """
                    staff_result = await query_db.execute(text(staff_query), {"user_uuid": update_data["service_personal"]})
                    staff_row = staff_result.fetchone()

                    if staff_row and staff_row._mapping.get("user_mobile"):
                        from module_admin.service.sms_service import SmsService

                        # 获取服务时间和地址
                        service_time = update_data.get("service_time") or order.get("service_time", "")
                        service_address = update_data.get("service_address") or order.get("service_address", "")

                        # 发送短信通知
                        await SmsService.send_service_assignment(
                            staff_row._mapping.get("user_mobile"),
                            order_number,
                            service_time,
                            service_address
                        )

                        logger.info(f"已向服务人员 {update_data['service_personal']} 发送短信通知")
                except Exception as e:
                    # 短信发送失败不影响主流程
                    logger.error(f"发送短信通知失败: {str(e)}")

            return result
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"更新服务订单数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"更新服务订单服务异常: {str(e)}")
            raise BusinessException(message=f"更新服务订单失败: {str(e)}")

    @classmethod
    async def find_serve_order_list_service(cls, query_db: AsyncSession, page: int, size: int, status: Optional[str] = None, commission_status: Optional[str] = None) -> Dict[str, Any]:
        """
        获取服务订单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :param commission_status: 佣金状态
        :return: 服务订单列表数据
        """
        try:
            # 调用DAO层获取服务订单列表
            serve_order_list, total, stat = await OrderDao.find_serve_order_list(query_db, page, size, status, commission_status)

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": serve_order_list,
                "total": total,
                "cert_tips": "",
                "stat": stat,
                "show_mobile": 2
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取服务订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取服务订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取服务订单列表失败: {str(e)}")

    @classmethod
    async def save_order_service(cls, query_db: AsyncSession, order_data: Dict) -> Dict:
        """
        保存订单信息服务

        :param query_db: 数据库会话
        :param order_data: 订单数据
        :return: 保存结果
        """
        try:
            # 验证订单数据
            if not order_data:
                raise ValidationException(message="订单数据不能为空")

            # 检查是否有订单ID，有则更新，无则创建
            order_id = order_data.get('id')

            if order_id:
                # 检查订单是否存在
                existing_order = await OrderDao.get_order_by_id(query_db, order_id)
                if not existing_order:
                    raise ResourceNotFoundException(message=f"订单不存在: {order_id}")

                # 调用DAO层更新订单
                result = await OrderDao.update_order(query_db, order_id, order_data)
                return result
            else:
                # 创建新订单
                new_order = await OrderDao.create_order(query_db, order_data)

                # 如果有订单详情数据，创建订单详情
                details_data = order_data.get('details', [])
                if details_data:
                    from module_admin.dao.order_details_dao import OrderDetailsDao
                    await OrderDetailsDao.create_order_details(query_db, new_order.id, details_data)

                # 如果有支付数据，创建支付记录
                payment_data = order_data.get('payment')
                if payment_data:
                    from module_admin.dao.order_payments_dao import OrderPaymentsDao
                    await OrderPaymentsDao.create_order_payment(query_db, new_order.id, payment_data)

                return {"id": new_order.id, "order_number": new_order.order_number, "message": "订单创建成功"}
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"保存订单数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"保存订单服务异常: {str(e)}")
            raise BusinessException(message=f"保存订单失败: {str(e)}")

    @classmethod
    async def get_order_detail_service(cls, query_db: AsyncSession, order_id: str) -> Dict:
        """
        获取订单详情服务

        :param query_db: 数据库会话
        :param order_id: 订单ID
        :return: 订单详情
        """
        try:
            # 获取订单基本信息
            order = await OrderDao.get_order_by_id(query_db, order_id)

            if not order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_id}")

            # 获取订单详情
            from module_admin.dao.order_details_dao import OrderDetailsDao
            details = await OrderDetailsDao.get_order_details(query_db, order_id)

            # 获取订单支付信息
            from module_admin.dao.order_payments_dao import OrderPaymentsDao
            payments = await OrderPaymentsDao.get_order_payments(query_db, order_id)

            # 获取订单服务人员信息
            from module_admin.dao.orderwaiter_dao import OrderWaiterDao
            waiters = await OrderWaiterDao.get_order_waiters(query_db, order['order_number'])

            # 构建完整的订单详情
            result = {
                **order,
                "details": details,
                "payments": payments,
                "waiters": waiters
            }

            return result
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单详情查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单详情服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单详情失败: {str(e)}")

    @classmethod
    async def find_order_detail_service(cls, query_db: AsyncSession, order_number: str) -> Dict:
        """
        获取订单详情服务（通过订单号）

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 订单详情
        """
        try:
            # 调用DAO层获取订单详情
            order_detail = await OrderDao.find_serve_order_detail(query_db, order_number)

            if not order_detail:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            return order_detail
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单详情查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单详情服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单详情失败: {str(e)}")

    @classmethod
    async def get_order_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None,
        keyword: Optional[str] = None,
        product_type: Optional[str] = None,
        order_new: Optional[str] = None
    ) -> Dict:
        """
        获取订单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :param keyword: 关键词
        :param product_type: 产品类型
        :param order_new: 订单新旧状态
        :return: 订单列表和分页信息
        """
        try:
            # 调用DAO层获取订单列表
            orders, total = await OrderDao.get_order_list(
                query_db, page, size, status, keyword, product_type, order_new
            )

            # 构建分页结果
            result = {
                "list": orders,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单列表失败: {str(e)}")

    @classmethod
    async def find_order_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        store_uuid: str,
        keyword: Optional[str] = None,
        order_status: Optional[str] = None,
        product_type: Optional[str] = None,
        order_new: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取订单列表服务（扩展支持筛选功能）

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param store_uuid: 门店UUID
        :param keyword: 搜索关键词
        :param order_status: 订单状态筛选
        :param product_type: 产品类型
        :param order_new: 订单新旧状态
        :return: 订单列表数据
        """
        try:
            # 调用DAO层获取订单列表
            order_list, total, order_stat = await OrderDao.find_order_list(
                query_db, page, size, store_uuid, keyword, order_status, product_type, order_new
            )

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": order_list,
                "total": total,
                "stat": order_stat,
                "order_stat_new": ""
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单列表失败: {str(e)}")

    @classmethod
    async def dispatch_order_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None,
        keyword: Optional[str] = None,
        order_type: Optional[str] = None,
        store_uuid: str = None
    ) -> Dict[str, Any]:
        """
        获取派单页面订单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :param keyword: 搜索关键词
        :param order_type: 订单类型
        :param store_uuid: 门店UUID
        :return: 订单列表数据
        """
        try:
            # 调用DAO层获取派单订单列表
            order_list, total = await OrderDao.get_dispatch_order_list(
                query_db, page, size, status, keyword, order_type, store_uuid
            )

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": order_list,
                "total": total,
                "total_pages": (total + size - 1) // size if total > 0 else 0
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取派单订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取派单订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取派单订单列表失败: {str(e)}")

    @classmethod
    async def dispatch_order_stats_service(
        cls,
        query_db: AsyncSession,
        store_uuid: str
    ) -> Dict[str, Any]:
        """
        获取派单页面订单统计服务

        :param query_db: 数据库会话
        :param store_uuid: 门店UUID
        :return: 订单统计数据
        """
        try:
            # 调用DAO层获取订单统计
            stats = await OrderDao.get_dispatch_order_stats(query_db, store_uuid)

            return stats
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取派单订单统计查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取派单订单统计服务异常: {str(e)}")
            raise BusinessException(message=f"获取派单订单统计失败: {str(e)}")

    @classmethod
    async def get_serve_order_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> Dict:
        """
        获取服务订单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :param keyword: 关键词
        :return: 服务订单列表和分页信息
        """
        try:
            # 调用DAO层获取服务订单列表
            orders, total = await OrderDao.get_serve_order_list(
                query_db, page, size, status, keyword
            )

            # 构建分页结果
            result = {
                "list": orders,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取服务订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取服务订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取服务订单列表失败: {str(e)}")

    @classmethod
    async def find_serve_order_list_service(cls, query_db: AsyncSession, page: int, size: int, status: Optional[str] = None, commission_status: Optional[str] = None) -> Dict[str, Any]:
        """
        获取服务订单列表服务（原有方法）

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :param commission_status: 佣金状态
        :return: 服务订单列表数据
        """
        try:
            # 调用DAO层获取服务订单列表
            serve_order_list, total, stat = await OrderDao.find_serve_order_list(query_db, page, size, status, commission_status)

            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": serve_order_list,
                "total": total,
                "cert_tips": "",
                "stat": stat,
                "show_mobile": 2
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取服务订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取服务订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取服务订单列表失败: {str(e)}")

    @classmethod
    async def get_serve_order_stat_service(
        cls,
        query_db: AsyncSession,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict:
        """
        获取服务订单统计信息服务

        :param query_db: 数据库会话
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 服务订单统计信息
        """
        try:
            # 调用DAO层获取服务订单统计信息
            stat_result = await OrderDao.get_serve_order_stat(
                query_db, start_date, end_date
            )

            return stat_result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取服务订单统计信息查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取服务订单统计信息服务异常: {str(e)}")
            raise BusinessException(message=f"获取服务订单统计信息失败: {str(e)}")

    @classmethod
    async def get_order_card_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None
    ) -> Dict:
        """
        获取订单卡片列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :return: 订单卡片列表和分页信息
        """
        try:
            # 调用DAO层获取订单卡片列表
            cards, total = await OrderDao.get_order_card_list(
                query_db, page, size, status
            )

            # 构建分页结果
            result = {
                "list": cards,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取订单卡片列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取订单卡片列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取订单卡片列表失败: {str(e)}")

    @classmethod
    async def update_order_status_service(
        cls,
        query_db: AsyncSession,
        order_id: str,
        status: int,
        status_name: str
    ) -> Dict:
        """
        更新订单状态服务

        :param query_db: 数据库会话
        :param order_id: 订单ID
        :param status: 订单状态
        :param status_name: 订单状态名称
        :return: 更新结果
        """
        try:
            # 验证订单状态
            if status < 0:
                raise ValidationException(message="订单状态无效")

            # 检查订单是否存在
            existing_order = await OrderDao.get_order_by_id(query_db, order_id)
            if not existing_order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_id}")

            # 调用DAO层更新订单状态
            success = await OrderDao.update_order_status(
                query_db, order_id, status, status_name
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            return {"id": order_id, "status": status, "status_name": status_name, "message": "订单状态更新成功"}
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"更新订单状态数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"更新订单状态业务异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"更新订单状态服务异常: {str(e)}")
            raise BusinessException(message=f"更新订单状态失败: {str(e)}")

    @classmethod
    async def cancel_order_service(
        cls,
        query_db: AsyncSession,
        order_number: str
    ) -> Dict:
        """
        取消订单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 取消结果
        """
        try:
            # 验证订单编号
            if not order_number:
                raise ValidationException(message="订单编号不能为空")

            # 根据订单编号获取订单
            order = await OrderDao.get_order_by_number(query_db, order_number)
            if not order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_number}")

            # 检查订单状态是否允许取消（状态<30或状态=40的订单才能取消）
            order_status = order.get("order_status")
            if order_status >= 30 and order_status != 40:
                status_name = cls._get_order_status_name(order_status)
                raise BusinessException(message=f"订单状态不允许取消，当前状态: {status_name}")

            # 直接更新本地数据库订单状态为99（已取消）
            success = await OrderDao.update_order_status(
                query_db, order['id'], 99, "已取消"
            )

            if not success:
                raise BusinessException(message="取消订单失败")

            return {
                "order_number": order_number,
                "order_id": order['id'],
                "status": 99,
                "status_name": "已取消",
                "message": "订单取消成功"
            }
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"取消订单数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"取消订单业务异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"取消订单服务异常: {str(e)}")
            raise BusinessException(message=f"取消订单失败: {str(e)}")

    @classmethod
    async def cancel_serve_order_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        cancel_reason: Optional[str] = None
    ) -> Dict:
        """
        取消服务订单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param cancel_reason: 取消原因
        :return: 取消结果
        """
        try:
            # 根据订单编号获取订单
            order = await OrderDao.get_order_by_number(query_db, order_number)

            if not order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_number}")

            # 检查订单状态是否可取消
            if order['order_status'] == 3:  # 假设状态3为已完成
                raise BusinessException(message="已完成的订单不能取消")

            # 更新订单状态为已取消
            success = await OrderDao.update_order_status(
                query_db, order['id'], 4, "已取消"  # 假设状态4为已取消
            )

            if not success:
                raise BusinessException(message="取消订单失败")

            # 记录取消原因
            if cancel_reason:
                # 记录订单日志
                log_query = """
                    INSERT INTO order_logs (
                        order_number, operation_type, operation_desc,
                        operator_id, operator_name, operation_time
                    ) VALUES (
                        :order_number, 4, :operation_desc,
                        0, 'system', NOW()
                    )
                """

                log_desc = f"取消订单原因: {cancel_reason}"
                await query_db.execute(
                    text(log_query),
                    {
                        "order_number": order_number,
                        "operation_desc": log_desc
                    }
                )

                # 更新订单备注
                remark_query = """
                    UPDATE `order`
                    SET remark = CASE
                        WHEN IFNULL(remark, '') = ''
                        THEN CONCAT('取消原因: ', :cancel_reason)
                        ELSE CONCAT(remark, '\n取消原因: ', :cancel_reason)
                    END,
                    update_time = NOW()
                    WHERE order_number = :order_number
                """

                await query_db.execute(
                    text(remark_query),
                    {
                        "order_number": order_number,
                        "cancel_reason": cancel_reason
                    }
                )

                await query_db.commit()

            return {"order_number": order_number, "message": "订单取消成功"}
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"取消服务订单业务异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"取消服务订单服务异常: {str(e)}")
            raise BusinessException(message=f"取消服务订单失败: {str(e)}")

    @classmethod
    async def update_serve_order_service(cls, query_db: AsyncSession, order_number: str, update_data: Dict[str, Any]) -> bool:
        """
        更新服务订单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param update_data: 更新数据
        :return: 更新是否成功
        """
        try:
            # 验证订单号
            if not order_number:
                raise ValidationException(message="订单号不能为空")

            # 调用DAO层更新服务订单
            result = await OrderDao.update_serve_order(query_db, order_number, update_data)

            return result
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"更新服务订单数据验证异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"更新服务订单服务异常: {str(e)}")
            raise BusinessException(message=f"更新服务订单失败: {str(e)}")

    @classmethod
    async def share_rob_order_service(
        cls,
        query_db: AsyncSession,
        order_number: str
    ) -> Dict:
        """
        分享抢单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 分享信息
        """
        try:
            # 根据订单编号获取订单
            order = await OrderDao.get_order_by_number(query_db, order_number)

            if not order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_number}")

            # 生成分享链接和二维码
            # 使用小程序页面路径而不是硬编码域名
            share_link = f"/pages-public/order-share?orderNumber={order_number}"
            # 使用api.qrserver.com生成二维码，与前端保持一致
            qr_code_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={share_link}"

            return {
                "order_number": order_number,
                "share_link": share_link,
                "qr_code_url": qr_code_url
            }
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"分享抢单服务异常: {str(e)}")
            raise BusinessException(message=f"分享抢单失败: {str(e)}")

    @classmethod
    async def set_meal_order_search_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        keyword: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict:
        """
        搜索套餐订单服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param keyword: 关键词
        :param status: 订单状态
        :return: 套餐订单列表和分页信息
        """
        try:
            # 调用DAO层获取套餐订单列表
            # 这里假设套餐订单的product_type为1
            orders, total = await OrderDao.get_order_list(
                query_db, page, size, status, keyword, product_type="1"
            )

            # 构建分页结果
            result = {
                "list": orders,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"搜索套餐订单查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"搜索套餐订单服务异常: {str(e)}")
            raise BusinessException(message=f"搜索套餐订单失败: {str(e)}")

    @classmethod
    async def set_meal_order_for_vue_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        page: int,
        size: int
    ) -> Dict:
        """
        获取用户套餐订单信息服务

        :param query_db: 数据库会话
        :param user_id: 用户ID
        :param page: 页码
        :param size: 每页数量
        :return: 用户套餐订单列表和分页信息
        """
        try:
            # 调用DAO层获取用户套餐订单列表
            # 这里假设套餐订单的product_type为2
            orders, total = await OrderDao.get_order_list(
                query_db, page, size, None, user_id, product_type="2"
            )

            # 构建分页结果
            result = {
                "list": orders,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取用户套餐订单信息查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取用户套餐订单信息服务异常: {str(e)}")
            raise BusinessException(message=f"获取用户套餐订单信息失败: {str(e)}")

    @classmethod
    async def job_order_for_vue_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> Dict:
        """
        获取工单列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 工单状态
        :param keyword: 关键词
        :return: 工单列表和分页信息
        """
        try:
            # 导入工单DAO
            from module_admin.dao.job_order_dao import JobOrderDao

            # 调用DAO层获取工单列表
            job_orders, total = await JobOrderDao.get_job_order_list(
                query_db, page, size, status, keyword
            )

            # 构建分页结果
            result = {
                "list": job_orders,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取工单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取工单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取工单列表失败: {str(e)}")

    @classmethod
    async def edit_job_order_save_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        job_order_data: Dict
    ) -> Dict:
        """
        编辑并保存工单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param job_order_data: 工单数据
        :return: 保存结果
        """
        try:
            # 根据订单编号获取订单
            order = await OrderDao.get_order_by_number(query_db, order_number)

            if not order:
                raise ResourceNotFoundException(message=f"订单不存在: {order_number}")

            # 导入工单DAO
            from module_admin.dao.job_order_dao import JobOrderDao

            # 检查是否已存在工单
            job_order = await JobOrderDao.get_job_order_by_order_id(query_db, order['id'])

            if job_order:
                # 更新现有工单
                result = await JobOrderDao.update_job_order(query_db, job_order['id'], job_order_data)
                return {"order_number": order_number, "job_order_id": job_order['id'], "message": "工单更新成功"}
            else:
                # 创建新工单
                job_order_data['order_id'] = order['id']
                result = await JobOrderDao.create_job_order(query_db, job_order_data)
                return {"order_number": order_number, "job_order_id": result['id'], "message": "工单创建成功"}
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"工单数据验证异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"编辑并保存工单服务异常: {str(e)}")
            raise BusinessException(message=f"编辑并保存工单失败: {str(e)}")

    @classmethod
    async def product_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None
    ) -> Dict:
        """
        获取产品列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 产品状态
        :return: 产品列表和分页信息
        """
        try:
            # 导入产品DAO
            from module_admin.dao.product_dao import ProductDao

            # 调用DAO层获取产品列表
            products, total = await ProductDao.get_product_list(
                query_db, page, size, status
            )

            # 构建分页结果
            result = {
                "list": products,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取产品列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取产品列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取产品列表失败: {str(e)}")

    @classmethod
    async def product_new_list_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        status: Optional[str] = None
    ) -> Dict:
        """
        获取新产品列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 产品状态
        :return: 新产品列表和分页信息
        """
        try:
            # 导入产品DAO
            from module_admin.dao.product_dao import ProductDao

            # 调用DAO层获取新产品列表
            products, total = await ProductDao.get_new_product_list(
                query_db, page, size, status
            )

            # 构建分页结果
            result = {
                "list": products,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取新产品列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取新产品列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取新产品列表失败: {str(e)}")

    @classmethod
    async def refresh_mini_qr_service(
        cls,
        query_db: AsyncSession,
        product_id: str
    ) -> Dict:
        """
        刷新小程序二维码服务

        :param query_db: 数据库会话
        :param product_id: 产品ID
        :return: 刷新结果
        """
        try:
            # 导入产品DAO
            from module_admin.dao.product_dao import ProductDao

            # 调用DAO层刷新小程序二维码
            result = await ProductDao.refresh_mini_qr(query_db, product_id)

            return result
        except DatabaseException as e:
            # 数据库异常，包装为资源不存在异常
            if "产品不存在" in e.message:
                logger.error(f"产品资源不存在: {e.message}")
                raise ResourceNotFoundException(message=e.message)
            # 其他数据库异常，直接向上传递
            logger.error(f"刷新小程序二维码数据库异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"刷新小程序二维码服务异常: {str(e)}")
            raise BusinessException(message=f"刷新小程序二维码失败: {str(e)}")

    @classmethod
    async def product_list_search_service(
        cls,
        query_db: AsyncSession,
        page: int,
        size: int,
        keyword: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict:
        """
        搜索产品列表服务

        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param keyword: 关键词
        :param status: 产品状态
        :return: 产品列表和分页信息
        """
        try:
            # 导入产品DAO
            from module_admin.dao.product_dao import ProductDao

            # 调用DAO层获取产品列表（带关键词搜索）
            products, total = await ProductDao.get_product_list(
                query_db, page, size, status, keyword
            )

            # 构建分页结果
            result = {
                "list": products,
                "total": total,
                "page": page,
                "size": size
            }

            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"搜索产品列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"搜索产品列表服务异常: {str(e)}")
            raise BusinessException(message=f"搜索产品列表失败: {str(e)}")

    @classmethod
    async def get_mini_style_base_info_service(
        cls,
        query_db: AsyncSession
    ) -> Dict:
        """
        获取小程序样式基本信息服务

        :param query_db: 数据库会话
        :return: 小程序样式基本信息
        """
        try:
            # 导入小程序样式DAO
            from module_admin.dao.mini_style_dao import MiniStyleDao

            # 调用DAO层获取小程序样式基本信息
            style_info = await MiniStyleDao.get_mini_style_base_info(query_db)

            return style_info
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取小程序样式基本信息服务异常: {str(e)}")
            raise BusinessException(message=f"获取小程序样式基本信息失败: {str(e)}")

    @classmethod
    async def get_dispatchable_staff_service(cls, query_db: AsyncSession, order_number: str) -> Dict[str, Any]:
        """
        获取可派单人员列表服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 可派单人员列表
        """
        try:
            # 验证订单号
            if not order_number:
                raise ValidationException(message="订单编号不能为空")

            # 获取订单信息
            order = await OrderDao.get_order_by_number(query_db, order_number)
            if not order:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            # 获取订单的产品ID和门店UUID
            product_id = order.get("product_id")  # 使用product_id字段
            store_uuid = order.get("store_uuid")

            if not product_id:
                raise ValidationException(message="订单缺少产品信息")

            if not store_uuid:
                raise ValidationException(message="订单缺少门店信息")

            # 调用DAO层获取可派单人员列表
            staff_list = await OrderDao.get_dispatchable_staff(query_db, product_id, store_uuid)

            # 获取订单金额，优先使用total_pay_actual，其次是payment_pay_actual，最后是pay_actual
            order_amount = float(order.get("total_pay_actual") or order.get("payment_pay_actual") or order.get("pay_actual") or 0)

            # 计算默认提成（订单金额 × 0.65）
            default_commission = round(order_amount * 0.65, 2)

            # 格式化服务时间 - 优先使用service_time，如果为空则组合service_date和service_hour
            service_time = order.get("service_time", "")
            if not service_time:
                service_date = order.get("service_date")
                service_hour = order.get("service_hour")
                service_time = cls._format_service_time(service_date, service_hour)

            # 构建返回数据
            result = {
                "order_info": {
                    "order_number": order_number,
                    "product_id": product_id,
                    "product_name": order.get("product_name", ""),
                    "store_uuid": store_uuid,
                    "store_name": order.get("store_name", ""),
                    "service_address": order.get("extend_service_address") or order.get("service_address", ""),
                    "service_time": service_time,
                    "customer_name": order.get("customer_name") or order.get("user_name", ""),
                    "customer_mobile": order.get("customer_mobile") or order.get("user_mobile", ""),
                    "order_amount": order_amount,
                    "default_commission": default_commission
                },
                "staff_list": staff_list,
                "total": len(staff_list)
            }

            logger.info(f"获取可派单人员成功，订单号: {order_number}, 找到 {len(staff_list)} 名人员")
            return result

        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"获取可派单人员数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取可派单人员查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取可派单人员服务异常: {str(e)}")
            raise BusinessException(message=f"获取可派单人员失败: {str(e)}")

    @classmethod
    async def set_preset_commission_service(cls, query_db: AsyncSession, order_number: str,
                                          preset_commission: float, store_uuid: str) -> Dict[str, Any]:
        """
        设置预设佣金服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param preset_commission: 预设佣金金额
        :param store_uuid: 门店UUID
        :return: 设置结果和分享信息
        """
        try:
            # 验证参数
            if not order_number:
                raise ValidationException(message="订单编号不能为空")

            if preset_commission <= 0:
                raise ValidationException(message="预设佣金必须大于0")

            # 获取订单信息
            order = await OrderDao.get_order_by_number(query_db, order_number)
            if not order:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            # 检查订单状态是否允许设置预设佣金
            order_status = order.get("order_status")
            if order_status != 10:  # 10=已接单
                raise ValidationException(message=f"订单状态不允许设置预设佣金，当前状态: {cls._get_order_status_name(order_status)}")

            # 检查订单是否属于当前门店
            if order.get("store_uuid") != store_uuid:
                raise ValidationException(message="无权限操作此订单")

            # 更新订单的预设佣金
            update_query = """
                UPDATE `order`
                SET preset_commission = :preset_commission,
                    update_time = NOW()
                WHERE order_number = :order_number
            """
            await query_db.execute(text(update_query), {
                "order_number": order_number,
                "preset_commission": preset_commission
            })

            # 生成分享链接
            share_url = f"/pages-public/order-share?orderNumber={order_number}&commission={preset_commission}"

            # 提交事务
            await query_db.commit()

            logger.info(f"设置预设佣金成功: 订单号={order_number}, 佣金={preset_commission}")

            return {
                "order_number": order_number,
                "preset_commission": preset_commission,
                "share_url": share_url,
                "message": "设置预设佣金成功"
            }

        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"设置预设佣金验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"订单资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"设置预设佣金服务异常: {str(e)}")
            raise BusinessException(message=f"设置预设佣金失败: {str(e)}")

    @classmethod
    async def assign_staff_service(cls, query_db: AsyncSession, order_number: str, staff_uuid: str,
                                 commission_amount: str = "0", service_reminder: str = "",
                                 notify_staff: bool = False) -> Dict[str, Any]:
        """
        派单确认服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param staff_uuid: 服务人员UUID
        :param commission_amount: 提成金额
        :param service_reminder: 服务提醒
        :param notify_staff: 是否通知服务人员
        :return: 派单结果
        """
        try:
            # 验证参数
            if not order_number:
                raise ValidationException(message="订单编号不能为空")

            if not staff_uuid:
                raise ValidationException(message="服务人员UUID不能为空")

            # 获取订单信息
            order = await OrderDao.get_order_by_number(query_db, order_number)
            if not order:
                raise ResourceNotFoundException(message=f"未找到订单号为{order_number}的订单")

            # 检查订单状态是否允许派单（已完成或已取消的订单不允许派单）
            order_status = order.get("order_status")
            if order_status in [80, 99]:  # 80=已完成, 99=已取消
                raise ValidationException(message=f"订单状态不允许派单，当前状态: {cls._get_order_status_name(order_status)}")

            # 获取服务人员信息
            staff_query = """
                SELECT id, uuid, real_name, mobile, status, store_uuid, company_id
                FROM service_staff
                WHERE uuid = :staff_uuid AND is_delete = '0'
            """
            staff_result = await query_db.execute(text(staff_query), {"staff_uuid": staff_uuid})
            staff_row = staff_result.fetchone()

            if not staff_row:
                raise ResourceNotFoundException(message=f"未找到服务人员: {staff_uuid}")

            staff_info = dict(staff_row._mapping)

            # 检查服务人员状态
            if staff_info.get("status") != "1":
                raise ValidationException(message="服务人员状态不正常，无法派单")

            # 检查服务人员是否属于同一门店
            if staff_info.get("store_uuid") != order.get("store_uuid"):
                raise ValidationException(message="服务人员与订单不属于同一门店")

            # 处理预派服务人员库存释放
            service_personal = order.get("service_personal")
            if service_personal:
                logger.info(f"发现预派服务人员: {service_personal}，开始释放库存")
                await cls._release_pre_assigned_staff_inventory(query_db, order_number, service_personal)

            # 检查是否已存在派单记录，判断是否为重派
            check_waiter_query = """
                SELECT id, service_id, service_name FROM order_waiter
                WHERE order_number = :order_number
            """
            waiter_result = await query_db.execute(text(check_waiter_query), {"order_number": order_number})
            existing_waiter = waiter_result.fetchone()

            is_redispatch = existing_waiter is not None

            if is_redispatch:
                # 这是重派操作，需要释放原员工库存
                old_staff_id = existing_waiter.service_id
                old_staff_name = existing_waiter.service_name

                logger.info(f"检测到重派操作，原派单员工: {old_staff_name} (ID: {old_staff_id})")

                # 释放原派单员工的库存
                await cls._release_assigned_staff_inventory(query_db, order_number, old_staff_id)

                # 更新现有派单记录
                update_waiter_query = """
                    UPDATE order_waiter
                    SET service_id = :service_id,
                        service_name = :service_name,
                        service_personal = :service_personal,
                        service_personal_commission = :service_personal_commission
                    WHERE order_number = :order_number
                """
                await query_db.execute(text(update_waiter_query), {
                    "order_number": order_number,
                    "service_id": staff_info.get("id"),  # 使用数字ID
                    "service_name": staff_info.get("real_name"),
                    "service_personal": commission_amount,
                    "service_personal_commission": 0  # 手动派单模式，commission字段为0
                })
            else:
                # 首次派单，创建新的派单记录
                insert_waiter_query = """
                    INSERT INTO order_waiter (
                        order_number, service_id, service_name, service_personal, service_personal_commission, create_time
                    ) VALUES (
                        :order_number, :service_id, :service_name, :service_personal, :service_personal_commission, NOW()
                    )
                """
                await query_db.execute(text(insert_waiter_query), {
                    "order_number": order_number,
                    "service_id": staff_info.get("id"),  # 使用数字ID
                    "service_name": staff_info.get("real_name"),
                    "service_personal": commission_amount,
                    "service_personal_commission": 0  # 手动派单模式，commission字段为0
                })

            # 获取订单的服务时间信息，用于更新库存
            service_time_query = """
                SELECT service_date, service_hour, product_sku_id, store_uuid
                FROM `order`
                WHERE order_number = :order_number
            """
            service_time_result = await query_db.execute(text(service_time_query), {"order_number": order_number})
            service_time_row = service_time_result.fetchone()

            if service_time_row:
                service_date = service_time_row._mapping.get("service_date")
                service_hour = service_time_row._mapping.get("service_hour")
                product_sku_id = service_time_row._mapping.get("product_sku_id")
                store_uuid_order = service_time_row._mapping.get("store_uuid")

                # 更新inventory库存 - 参考create_order逻辑
                if service_hour and service_date and product_sku_id:
                    try:
                        # 解析服务时间

                        # 从service_date中提取日期部分
                        if isinstance(service_date, str):
                            service_day = service_date.split(' ')[0]  # 获取日期部分 YYYY-MM-DD
                        else:
                            service_day = service_date.strftime('%Y-%m-%d')

                        # 解析服务小时
                        time_parts = service_hour.split(':')
                        if len(time_parts) >= 2:
                            hour, minute = int(time_parts[0]), int(time_parts[1])
                        else:
                            logger.warning(f"服务时间格式异常: {service_hour}, 使用默认值")
                            hour, minute = 9, 0  # 默认上午9点

                        # 计算时间段索引
                        time_slot = hour * 2 + 1
                        if minute >= 30:
                            time_slot += 1

                        # 获取SKU的持续时间信息
                        sku_duration_query = """
                            SELECT type_price_unit, duration FROM product_sku
                            WHERE id = :sku_id
                        """
                        sku_result = await query_db.execute(text(sku_duration_query), {"sku_id": product_sku_id})
                        sku_row = sku_result.fetchone()

                        # 确定服务持续时间
                        if sku_row:
                            base_duration = sku_row._mapping.get("duration") or 60  # 默认60分钟
                        else:
                            base_duration = 60  # 默认60分钟

                        # 计算需要连续的时间段数量
                        required_slots = math.ceil(base_duration / 30)

                        logger.info(f"更新库存: 员工{staff_info.get('uuid')}, 日期{service_day}, 时间段{time_slot}, 持续{required_slots}个时间段")

                        # 更新库存 - 将对应时间段设置为不可用
                        for slot in range(time_slot, time_slot + required_slots):
                            if slot > 48:  # 超出一天的时间范围
                                break

                            update_inventory_query = f"""
                                UPDATE inventory
                                SET time{slot} = 2
                                WHERE service_staff_id = :staff_id
                                AND store_uuid = :store_uuid
                                AND day = :day_date
                            """

                            await query_db.execute(text(update_inventory_query), {
                                "staff_id": staff_info.get("id"),  # 使用数字ID而不是UUID
                                "store_uuid": store_uuid_order,
                                "day_date": service_day
                            })

                            logger.info(f"已更新员工{staff_info.get('uuid')}的时间段{slot}为不可用")

                    except Exception as e:
                        logger.error(f"更新库存失败: {str(e)}")
                        # 库存更新失败不影响派单，只记录日志

            # 插入派单操作日志
            try:
                log_query = """
                    INSERT INTO order_logs (
                        order_number, operation_type, operation_desc,
                        operator_id, operator_name, operation_time, create_time
                    ) VALUES (
                        :order_number, 5, :operation_desc,
                        :operator_id, :operator_name, NOW(), NOW()
                    )
                """

                if is_redispatch:
                    operation_desc = f"重派订单: 从 {old_staff_name} 重派给 {staff_info.get('real_name')} ({staff_info.get('mobile')})"
                else:
                    operation_desc = f"派单给服务人员: {staff_info.get('real_name')} ({staff_info.get('mobile')})"

                await query_db.execute(text(log_query), {
                    "order_number": order_number,
                    "operation_desc": operation_desc,
                    "operator_id": staff_info.get("id"),  # 使用数字ID而不是UUID
                    "operator_name": staff_info.get("real_name")
                })

                logger.info(f"已插入派单操作日志: {operation_desc}")

            except Exception as e:
                logger.error(f"插入派单日志失败: {str(e)}")
                # 日志插入失败不影响派单，只记录错误

            # 更新订单状态为派单待确认，同时更新服务提醒并清空预派服务人员
            update_order_query = """
                UPDATE `order`
                SET order_status = 20,
                    order_status_name = '派单待确认',
                    service_remark = :service_remark,
                    service_personal = NULL,
                    update_time = NOW()
                WHERE order_number = :order_number
            """
            await query_db.execute(text(update_order_query), {
                "order_number": order_number,
                "service_remark": service_reminder
            })

            # 在提交事务前调用第三方员工库存接口
            staff_mobile = staff_info.get("mobile")
            service_date = order.get('service_date')
            service_hour = order.get('service_hour')

            if staff_mobile and service_date and service_hour:
                logger.info(f"准备调用第三方员工库存接口 - 员工: {staff_mobile}, 日期: {service_date}, 时间: {service_hour}")

                # 调用第三方接口
                api_success = await cls._call_employee_inventory_api(staff_mobile, service_date, service_hour)

                if not api_success:
                    # 第三方接口调用失败，回滚事务并抛出异常
                    await query_db.rollback()
                    logger.error(f"第三方员工库存接口调用失败，取消派单 - 订单号: {order_number}, 员工: {staff_info.get('real_name')}")
                    raise BusinessException(message="第三方员工库存接口调用失败，派单取消")

                logger.info(f"第三方员工库存接口调用成功，继续派单流程 - 订单号: {order_number}")
            else:
                logger.warning(f"缺少必要参数，跳过第三方接口调用 - 手机号: {staff_mobile}, 日期: {service_date}, 时间: {service_hour}")

            # 提交事务
            await query_db.commit()

            # 发送微信派单通知
            try:
                from module_admin.service.wechat_official_service import WechatOfficialService

                # 构建推送数据
                push_result = await WechatOfficialService.send_dispatch_notification(
                    db=query_db,
                    staff_uuid=staff_uuid,
                    order_number=order_number,
                    service_name=order.get('product_name', '家政服务'),
                    service_time=order.get('service_date', ''),
                    service_address=order.get('service_address', ''),
                    customer_name=order.get('customer_name', ''),
                    customer_phone=order.get('service_phone', ''),
                    service_reminder=service_reminder
                )

                if push_result.get('success'):
                    logger.info(f"派单微信推送成功 - 订单号: {order_number}, 服务人员: {staff_info.get('real_name')}")
                else:
                    logger.warning(f"派单微信推送失败 - 订单号: {order_number}, 错误: {push_result.get('message')}")

            except Exception as e:
                # 推送失败不影响派单成功
                logger.error(f"派单微信推送异常 - 订单号: {order_number}, 错误: {str(e)}")

            # 如果需要通知服务人员（短信等其他方式）
            if notify_staff and staff_info.get("mobile"):
                try:
                    # 这里可以集成短信服务
                    logger.info(f"需要向服务人员 {staff_info.get('real_name')} ({staff_info.get('mobile')}) 发送派单通知")
                except Exception as e:
                    logger.error(f"发送派单通知失败: {str(e)}")

            result = {
                "order_number": order_number,
                "staff_info": {
                    "uuid": staff_info.get("uuid"),
                    "real_name": staff_info.get("real_name"),
                    "mobile": staff_info.get("mobile")
                },
                "message": "派单成功"
            }

            logger.info(f"派单成功，订单号: {order_number}, 服务人员: {staff_info.get('real_name')}")
            return result

        except ValidationException as e:
            await query_db.rollback()
            logger.error(f"派单数据验证异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            await query_db.rollback()
            logger.error(f"派单资源不存在: {e.message}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"派单服务异常: {str(e)}")
            raise BusinessException(message=f"派单失败: {str(e)}")

    @classmethod
    async def _release_assigned_staff_inventory(cls, query_db: AsyncSession, order_number: str, staff_id: int):
        """
        释放已派单员工的库存占用

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param staff_id: 已派单员工ID
        """
        try:
            # 获取订单的服务时间信息
            service_time_query = """
                SELECT service_date, service_hour, product_sku_id, store_uuid
                FROM `order`
                WHERE order_number = :order_number
            """
            service_time_result = await query_db.execute(text(service_time_query), {"order_number": order_number})
            service_time_row = service_time_result.fetchone()

            if not service_time_row:
                logger.warning(f"未找到订单 {order_number} 的服务时间信息")
                return

            service_date = service_time_row.service_date
            service_hour = service_time_row.service_hour
            product_sku_id = service_time_row.product_sku_id
            store_uuid_order = service_time_row.store_uuid

            if not service_date or not service_hour:
                logger.warning(f"订单 {order_number} 缺少服务时间信息")
                return

            # 获取产品基础时长
            product_query = """
                SELECT base_duration FROM service_product
                WHERE sku_id = :sku_id
            """
            product_result = await query_db.execute(text(product_query), {"sku_id": product_sku_id})
            product_row = product_result.fetchone()

            if not product_row:
                logger.warning(f"未找到产品信息，SKU ID: {product_sku_id}")
                return

            base_duration = product_row.base_duration or 60  # 默认60分钟

            # 解析服务时间
            service_day = service_date.strftime('%Y-%m-%d')
            time_parts = service_hour.split(':')
            if len(time_parts) >= 2:
                hour, minute = int(time_parts[0]), int(time_parts[1])
            else:
                logger.warning(f"服务时间格式异常: {service_hour}, 使用默认值")
                hour, minute = 9, 0  # 默认上午9点
            time_slot = hour * 2 + 1
            if minute >= 30:
                time_slot += 1

            # 计算需要连续的时间段数量
            required_slots = math.ceil(base_duration / 30)

            logger.info(f"释放已派单员工库存: 员工{staff_id}, 日期{service_day}, 时间段{time_slot}, 持续{required_slots}个时间段")

            # 释放库存 - 将对应时间段设置为可用
            for slot in range(time_slot, time_slot + required_slots):
                if slot > 48:  # 超出一天的时间范围
                    break

                release_inventory_query = f"""
                    UPDATE inventory
                    SET time{slot} = 1
                    WHERE service_staff_id = :staff_id
                    AND store_uuid = :store_uuid
                    AND day = :day_date
                """

                await query_db.execute(text(release_inventory_query), {
                    "staff_id": staff_id,
                    "store_uuid": store_uuid_order,
                    "day_date": service_day
                })

                logger.info(f"已释放已派单员工{staff_id}的时间段{slot}为可用")

        except Exception as e:
            logger.error(f"释放已派单员工库存失败: {str(e)}")
            # 库存释放失败不影响重派，只记录日志

    @classmethod
    async def _release_pre_assigned_staff_inventory(cls, query_db: AsyncSession, order_number: str, service_personal: str):
        """
        释放预派服务人员的库存占用

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param service_personal: 预派服务人员ID字符串（可能多个，用逗号分隔）
        """
        try:
            # 解析预派服务人员ID列表
            staff_ids = [staff_id.strip() for staff_id in service_personal.split(',') if staff_id.strip()]

            if not staff_ids:
                logger.info("没有找到有效的预派服务人员ID")
                return

            # 获取订单的服务时间信息
            service_time_query = """
                SELECT service_date, service_hour, product_sku_id, store_uuid
                FROM `order`
                WHERE order_number = :order_number
            """
            service_time_result = await query_db.execute(text(service_time_query), {"order_number": order_number})
            service_time_row = service_time_result.fetchone()

            if not service_time_row:
                logger.warning(f"未找到订单 {order_number} 的服务时间信息")
                return

            service_date = service_time_row._mapping.get("service_date")
            service_hour = service_time_row._mapping.get("service_hour")
            product_sku_id = service_time_row._mapping.get("product_sku_id")
            store_uuid_order = service_time_row._mapping.get("store_uuid")

            if not (service_hour and service_date and product_sku_id):
                logger.warning(f"订单 {order_number} 的服务时间信息不完整")
                return

            # 解析服务时间
            if isinstance(service_date, str):
                service_day = service_date.split(' ')[0]  # 获取日期部分 YYYY-MM-DD
            else:
                service_day = service_date.strftime('%Y-%m-%d')

            # 解析服务小时
            time_parts = service_hour.split(':')
            if len(time_parts) >= 2:
                hour, minute = int(time_parts[0]), int(time_parts[1])
            else:
                logger.warning(f"服务时间格式异常: {service_hour}, 使用默认值")
                hour, minute = 9, 0  # 默认上午9点

            # 计算时间段索引
            time_slot = hour * 2 + 1
            if minute >= 30:
                time_slot += 1

            # 获取SKU的持续时间信息
            sku_duration_query = """
                SELECT type_price_unit, duration FROM product_sku
                WHERE id = :sku_id
            """
            sku_result = await query_db.execute(text(sku_duration_query), {"sku_id": product_sku_id})
            sku_row = sku_result.fetchone()

            # 确定服务持续时间
            if sku_row:
                base_duration = sku_row._mapping.get("duration") or 60  # 默认60分钟
            else:
                base_duration = 60  # 默认60分钟

            # 计算需要连续的时间段数量
            required_slots = math.ceil(base_duration / 30)

            logger.info(f"释放预派服务人员库存: 日期{service_day}, 时间段{time_slot}, 持续{required_slots}个时间段")

            # 为每个预派服务人员释放库存
            for staff_id in staff_ids:
                try:
                    # 释放库存 - 将对应时间段设置为可用
                    for slot in range(time_slot, time_slot + required_slots):
                        if slot > 48:  # 超出一天的时间范围
                            break

                        release_inventory_query = f"""
                            UPDATE inventory
                            SET time{slot} = 1
                            WHERE service_staff_id = :staff_id
                            AND store_uuid = :store_uuid
                            AND day = :day_date
                        """

                        await query_db.execute(text(release_inventory_query), {
                            "staff_id": staff_id,
                            "store_uuid": store_uuid_order,
                            "day_date": service_day
                        })

                        logger.info(f"已释放预派员工{staff_id}的时间段{slot}为可用")

                except Exception as e:
                    logger.error(f"释放预派员工{staff_id}库存失败: {str(e)}")
                    # 单个员工释放失败不影响其他员工，继续处理

            # 清空订单的预派服务人员字段
            clear_service_personal_query = """
                UPDATE `order`
                SET service_personal = NULL
                WHERE order_number = :order_number
            """
            await query_db.execute(text(clear_service_personal_query), {"order_number": order_number})

            logger.info(f"已清空订单 {order_number} 的预派服务人员字段")

        except Exception as e:
            logger.error(f"释放预派服务人员库存失败: {str(e)}")
            # 预派库存释放失败不影响主要派单流程，只记录错误

    @staticmethod
    async def _call_employee_inventory_api(staff_mobile: str, service_date: str, service_hour: str) -> bool:
        """
        调用第三方员工库存接口

        :param staff_mobile: 员工手机号
        :param service_date: 服务日期 (datetime对象或字符串)
        :param service_hour: 服务时间 (如 "14:30")
        :return: 是否调用成功 (HTTP状态码200)
        """
        try:
            # 处理日期格式
            if isinstance(service_date, datetime.datetime):
                day = service_date.strftime('%Y-%m-%d')
            elif isinstance(service_date, str):
                # 如果是字符串，提取日期部分
                day = service_date.split(' ')[0] if ' ' in service_date else service_date
            else:
                logger.error(f"无效的服务日期格式: {service_date}")
                return False

            # 从服务时间中提取小时数
            try:
                if ':' in service_hour:
                    hour_str = service_hour.split(':')[0]
                    starthour = int(hour_str)
                else:
                    starthour = int(service_hour)

                # 验证小时数范围
                if not (0 <= starthour <= 23):
                    logger.error(f"无效的小时数: {starthour}")
                    return False

            except (ValueError, IndexError) as e:
                logger.error(f"解析服务时间失败: {service_hour}, 错误: {str(e)}")
                return False

            # 构建请求参数
            request_data = {
                "phone": staff_mobile,
                "day": day,
                "starthour": starthour
            }

            logger.info(f"调用第三方员工库存接口，参数: {request_data}")

            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    "https://ortherapi.xiaoyujia.com/EmployeeInventory",
                    json=request_data,
                    headers={
                        "Content-Type": "application/json"
                    }
                )

                logger.info(f"第三方接口响应状态码: {response.status_code}")
                logger.info(f"第三方接口响应内容: {response.text}")

                # 检查状态码是否为200
                if response.status_code == 200:
                    logger.info(f"第三方员工库存接口调用成功 - 员工: {staff_mobile}, 日期: {day}, 时间: {starthour}")
                    return True
                else:
                    logger.error(f"第三方员工库存接口返回非200状态码: {response.status_code}")
                    return False

        except httpx.TimeoutException:
            logger.error("第三方员工库存接口请求超时")
            return False
        except httpx.HTTPStatusError as e:
            logger.error(f"第三方员工库存接口HTTP错误: {e.response.status_code}")
            return False
        except Exception as e:
            logger.error(f"调用第三方员工库存接口异常: {str(e)}")
            return False

    @classmethod
    async def create_proxy_order_service(cls, query_db: AsyncSession, order_data, current_user) -> Dict[str, Any]:
        """
        创建代客下单服务

        :param query_db: 数据库会话
        :param order_data: 订单数据
        :param current_user: 当前用户信息
        :return: 创建的订单信息
        """
        try:
            # 1. 获取当前用户的门店UUID
            if not current_user or not current_user.user or not current_user.user.store_uuid:
                raise BusinessException(message="用户未关联门店，无法创建订单", code=1002)

            user_store_uuid = current_user.user.store_uuid
            logger.info(f"使用当前用户的门店UUID: {user_store_uuid}")

            # 2. 验证门店信息
            store_info = await cls._validate_store(query_db, user_store_uuid)

            # 2. 验证产品和SKU信息
            product_info, sku_info = await cls._validate_product_sku(
                query_db, order_data.product_info.product_uuid, order_data.product_info.sku_id
            )

            # 3. 处理客户信息和地址
            customer_info, address_id = await cls._handle_customer_and_address(
                query_db, order_data.customer_info, order_data.address_info, store_info, user_store_uuid
            )

            # 4. 处理支付方式（在订单创建前处理，余额支付需要立即扣费）
            payment_result = None
            order_number = None
            if order_data.order_info.pay_type == 'BALANCE':
                # 余额支付：先生成订单号，再立即扣费
                order_number = cls._generate_proxy_order_number()
                payment_result = await cls._process_balance_payment_for_proxy_order(
                    query_db, current_user, order_data.order_info.amount,
                    order_number,  # 使用预生成的订单号
                    order_data.customer_info.name,  # 直接使用前端传入的姓名
                    order_data.customer_info.phone  # 直接使用前端传入的手机号
                )
            # 现金支付：不立即扣费，创建待付款订单

            # 5. 生成订单数据
            order_dict = await cls._build_proxy_order_data(
                order_data, store_info, product_info, sku_info, current_user, customer_info, address_id, user_store_uuid
            )

            # 6. 创建订单记录
            order_result = await cls._create_proxy_order_record(query_db, order_dict, payment_result, order_number)

            # 7. 余额支付时订单号已经在扣费时使用了正确的值，不需要再更新

            # 7. 创建相关记录（地址、备注等）
            await cls._create_proxy_related_records(query_db, order_result['order_id'], order_data)

            # 8. 提交事务
            await query_db.commit()

            return {
                "order_id": order_result['order_id'],
                "order_number": order_result['order_number'],
                "order_status": 10,  # 已接单
                "order_status_name": "已接单",
                "total_amount": order_data.order_info.amount,
                "product_name": product_info.get('product_name', '家政服务'),  # 添加产品名称
                "create_time": datetime.datetime.now()
            }

        except ValidationException as e:
            await query_db.rollback()
            raise e
        except BusinessException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"创建代客下单失败: {str(e)}")
            raise BusinessException(message=f"创建订单失败: {str(e)}")

    @classmethod
    async def cash_payment_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        current_user
    ) -> Dict[str, Any]:
        """现金支付服务"""
        try:
            # 1. 查询订单信息
            order_info = await OrderDao.get_order_by_number(query_db, order_number)
            if not order_info:
                raise BusinessException(message="订单不存在")

            # 2. 检查订单支付状态
            pay_status = order_info.get('pay_status')
            if pay_status == 1:
                raise BusinessException(message="订单已支付，无需重复支付")

            # 3. 获取订单金额和客户ID
            pay_actual = order_info.get('pay_actual', 0) or order_info.get('pay_money', 0)
            order_amount = float(pay_actual) if pay_actual else 0.0
            if order_amount <= 0:
                raise BusinessException(message="订单金额异常")

            user_id = order_info.get('user_id')
            if not user_id:
                raise BusinessException(message="订单未关联客户信息")

            # 4. 查询客户余额信息
            customer_info = await cls._get_customer_balance(query_db, user_id)
            if not customer_info:
                raise BusinessException(message="客户信息不存在")

            customer_uuid = customer_info.get('uuid')
            # 处理amount字段为NULL的情况
            amount_value = customer_info.get('amount')
            if amount_value is None:
                amount_value = 0
            customer_balance = float(amount_value)
            customer_name = customer_info.get('name', '')
            customer_mobile = customer_info.get('mobile', '')

            if not customer_uuid:
                raise BusinessException(message="客户UUID获取失败")

            # 5. 现金支付不需要验证余额是否足够，只需要确认客户信息存在
            # 现金支付是线下收取现金，只需要记录支付流水即可
            logger.info(f"现金支付处理：客户余额 {customer_balance:.2f}元，订单金额 {order_amount:.2f}元，无需验证余额充足性")

            # 6. 获取操作人信息
            operator_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
            operator_name = getattr(current_user.user, 'name', '') or getattr(current_user.user, 'user_name', '')

            if not operator_id:
                raise BusinessException(message="操作人信息获取失败")

            # 7. 插入现金支付流水记录
            transaction_result = await cls._create_cash_payment_transaction(
                query_db, customer_uuid, order_number, order_amount, customer_balance,
                operator_id, operator_name, customer_name, customer_mobile
            )

            # 8. 更新订单支付状态
            await cls._update_order_payment_status(query_db, order_number, transaction_result.get('transaction_no', ''))

            await query_db.commit()

            return {
                "order_number": order_number,
                "amount": order_amount,
                "transaction_id": transaction_result.get('transaction_no', ''),
                "customer_balance": customer_balance,
                "message": "现金支付成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"现金支付失败: {str(e)}")
            raise BusinessException(message=f"现金支付失败: {str(e)}")

    @classmethod
    async def manual_balance_payment_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        current_user
    ) -> Dict[str, Any]:
        """手动余额扣费服务"""
        try:
            # 1. 查询订单信息
            order_info = await OrderDao.get_order_by_number(query_db, order_number)
            if not order_info:
                raise BusinessException(message="订单不存在")

            # 2. 检查订单支付状态
            pay_status = order_info.get('pay_status')
            if pay_status == 1:
                raise BusinessException(message="订单已支付，无需重复扣费")
            elif pay_status is None:
                raise BusinessException(message="订单支付记录不存在，请联系管理员")

            # 3. 获取订单金额 - 优先使用最新的total_pay_actual字段
            # 按优先级获取订单金额：total_pay_actual > pay_actual > pay_money
            total_pay_actual = order_info.get('total_pay_actual', 0)
            pay_actual = order_info.get('pay_actual', 0)
            pay_money = order_info.get('pay_money', 0)

            # 选择最新的金额
            if total_pay_actual and float(total_pay_actual) > 0:
                order_amount = float(total_pay_actual)
                logger.info(f"使用total_pay_actual金额: {order_amount}")
            elif pay_actual and float(pay_actual) > 0:
                order_amount = float(pay_actual)
                logger.info(f"使用pay_actual金额: {order_amount}")
            elif pay_money and float(pay_money) > 0:
                order_amount = float(pay_money)
                logger.info(f"使用pay_money金额: {order_amount}")
            else:
                order_amount = 0.0

            if order_amount <= 0:
                raise BusinessException(message="订单金额异常，无法进行扣费")

            # 4. 检查用户余额是否充足
            user_id = getattr(current_user.user, 'uuid', None)
            user_name = getattr(current_user.user, 'name', '')

            if not user_id:
                raise BusinessException(message="用户信息获取失败")

            # 检查余额充足性
            from module_admin.service.payment_service import PaymentService
            try:
                balance_info = await PaymentService.get_user_balance_service(query_db, user_id)
                available_balance = float(balance_info.get('available', 0))
                if available_balance < order_amount:
                    raise BusinessException(message=f"余额不足，当前余额：¥{available_balance:.2f}，需要：¥{order_amount:.2f}")
            except Exception as e:
                if "余额不足" in str(e):
                    raise e
                logger.warning(f"获取余额信息失败，继续执行扣费: {str(e)}")

            # 5. 进行余额扣费
            # 获取客户信息用于流水记录
            customer_name = order_info.get('customer_name', '')
            customer_mobile = order_info.get('customer_mobile', '')

            # 判断原始支付方式，如果是现金支付的手动扣费，显示为"现金"
            original_pay_type = order_info.get('pay_type', 1)  # 默认现金支付
            payment_type = "现金" if original_pay_type in [1, '1', 106, '106'] else "余额"

            payment_result = await cls._process_balance_deduction(
                query_db, user_id, user_name, order_amount, order_number, customer_name, customer_mobile, payment_type
            )

            # 6. 更新订单支付状态和交易凭证
            transaction_no = payment_result.get('transaction_no', '')
            await cls._update_order_payment_status(
                query_db, order_number, transaction_no
            )

            await query_db.commit()

            return {
                "order_number": order_number,
                "amount": order_amount,
                "transaction_id": transaction_no,
                "balance_before": payment_result.get('balance_before', 0),
                "balance_after": payment_result.get('balance_after', 0),
                "message": "余额扣费成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"手动余额扣费失败: {str(e)}")
            raise BusinessException(message=f"余额扣费失败: {str(e)}")

    @classmethod
    async def _update_order_payment_status(
        cls,
        query_db: AsyncSession,
        order_number: str,
        transaction_id: str
    ):
        """更新订单支付状态"""
        try:
            # 更新支付记录
            update_payment_sql = """
                UPDATE order_payments
                SET pay_status = 1, pay_time = NOW(), transaction_id = :transaction_id
                WHERE order_number = :order_number
            """

            await query_db.execute(text(update_payment_sql), {
                "order_number": order_number,
                "transaction_id": transaction_id
            })

            logger.info(f"更新订单支付状态成功: {order_number}")

        except Exception as e:
            logger.error(f"更新订单支付状态失败: {str(e)}")
            raise BusinessException(message=f"更新支付状态失败: {str(e)}")

    @classmethod
    async def _validate_store(cls, query_db: AsyncSession, store_uuid: str) -> Dict[str, Any]:
        """验证门店信息"""
        try:
            store_query = """
                SELECT id, name, status
                FROM store
                WHERE store_uuid = :store_uuid
            """
            result = await query_db.execute(text(store_query), {"store_uuid": store_uuid})
            store_row = result.fetchone()

            if not store_row:
                raise BusinessException(message="门店不存在或已停用", code=1002)

            store_info = store_row._mapping
            if store_info.get("status") != 1:
                raise BusinessException(message="门店已停用", code=1002)

            return dict(store_info)
        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"验证门店信息失败: {str(e)}")
            raise BusinessException(message="门店信息验证失败", code=1002)

    @classmethod
    async def _validate_product_sku(cls, query_db: AsyncSession, product_uuid: str, sku_id: int):
        """验证产品和SKU信息"""
        try:
            # 使用JOIN查询确保产品和SKU匹配
            combined_query = """
                SELECT
                    p.id as product_id,
                    p.product_name,
                    p.type,
                    p.is_delete,
                    p.product_status,
                    s.id as sku_id,
                    s.name as sku_name,
                    s.now_price as sku_price,
                    s.define_commission as sku_commission,
                    s.commission_type as sku_commission_type
                FROM product p
                INNER JOIN product_sku s ON p.id = s.productid
                WHERE p.uuid = :product_uuid AND s.id = :sku_id
            """
            result = await query_db.execute(text(combined_query), {
                "product_uuid": product_uuid,
                "sku_id": sku_id
            })
            row = result.fetchone()

            if not row:
                raise BusinessException(message="产品或SKU不存在", code=1003)

            row_data = dict(row._mapping)

            # 验证产品状态
            if row_data.get("is_delete") == 1 or row_data.get("product_status") != 1:
                raise BusinessException(message="产品已下架", code=1003)

            # 构建产品信息
            product_info = {
                "id": row_data["product_id"],
                "product_name": row_data["product_name"],
                "type": row_data["type"],
                "is_delete": row_data["is_delete"],
                "product_status": row_data["product_status"]
            }

            # 构建SKU信息
            sku_info = {
                "id": row_data["sku_id"],
                "name": row_data["sku_name"],
                "now_price": row_data["sku_price"],
                "define_commission": row_data["sku_commission"],
                "commission_type": row_data["sku_commission_type"]
            }

            return product_info, sku_info
        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"验证产品SKU信息失败: {str(e)}")
            raise BusinessException(message="产品信息验证失败", code=1003)

    @classmethod
    async def _handle_customer_and_address(
        cls,
        query_db: AsyncSession,
        customer_info,
        address_info,
        store_info: Dict[str, Any],
        user_store_uuid: str
    ):
        """处理客户信息和地址"""
        try:
            # 1. 根据手机号和门店UUID查询客户
            customer_query = """
                SELECT id, name, mobile, store_uuid
                FROM ccuser
                WHERE mobile = :mobile AND store_uuid = :store_uuid
            """
            customer_result = await query_db.execute(text(customer_query), {
                "mobile": customer_info.phone,
                "store_uuid": user_store_uuid
            })
            customer_row = customer_result.fetchone()

            if customer_row:
                # 客户已存在，获取客户信息
                customer_data = dict(customer_row._mapping)
                logger.info(f"找到已存在客户: {customer_data['id']}")
            else:
                # 客户不存在，创建新客户
                customer_data = await cls._create_new_customer(
                    query_db, customer_info, store_info, user_store_uuid
                )
                logger.info(f"创建新客户: {customer_data['id']}")

            # 2. 创建地址记录
            address_id = await cls._create_customer_address(
                query_db, customer_data['id'], customer_info, address_info
            )

            return customer_data, address_id

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"处理客户信息和地址失败: {str(e)}")
            raise BusinessException(message="客户信息处理失败", code=1008)

    @classmethod
    async def _create_new_customer(
        cls,
        query_db: AsyncSession,
        customer_info,
        store_info: Dict[str, Any],
        user_store_uuid: str
    ) -> Dict[str, Any]:
        """创建新客户"""
        try:
            import uuid
            customer_uuid = str(uuid.uuid4())

            # 插入新客户记录
            insert_customer_sql = """
                INSERT INTO ccuser (uuid, name, mobile, store_uuid, created_by, updated_by, created_at, updated_at)
                VALUES (:uuid, :name, :mobile, :store_uuid, 'proxy_order', 'proxy_order', NOW(), NOW())
            """
            await query_db.execute(text(insert_customer_sql), {
                "uuid": customer_uuid,
                "name": customer_info.name,
                "mobile": customer_info.phone,
                "store_uuid": user_store_uuid
            })

            # 查询新创建的客户信息
            customer_query = """
                SELECT id, uuid, name, mobile, store_uuid
                FROM ccuser
                WHERE uuid = :uuid
            """
            customer_result = await query_db.execute(text(customer_query), {"uuid": customer_uuid})
            customer_row = customer_result.fetchone()

            if not customer_row:
                raise BusinessException(message="创建客户后查询失败", code=1008)

            return dict(customer_row._mapping)

        except Exception as e:
            logger.error(f"创建新客户失败: {str(e)}")
            raise BusinessException(message="创建客户失败", code=1008)

    @classmethod
    async def _create_customer_address(
        cls,
        query_db: AsyncSession,
        customer_id: int,
        customer_info,
        address_info
    ) -> int:
        """创建客户地址"""
        try:
            # 插入地址记录到ccuser_extend表
            insert_address_sql = """
                INSERT INTO ccuser_extend (
                    ccuser_id, name, address, address_desc, lng, lat,
                    city, contact_phone, isDefault, created_by, updated_by,
                    created_at, updated_at
                ) VALUES (
                    :ccuser_id, :name, :address, :address_desc, :lng, :lat,
                    :city, :contact_phone, 1, 'proxy_order', 'proxy_order',
                    NOW(), NOW()
                )
            """
            await query_db.execute(text(insert_address_sql), {
                "ccuser_id": str(customer_id),
                "name": customer_info.name,
                "address": address_info.address,
                "address_desc": address_info.door_number or "",
                "lng": str(address_info.longitude),
                "lat": str(address_info.latitude),
                "city": address_info.city or "",
                "contact_phone": customer_info.phone
            })

            # 获取新插入的地址ID - 查询刚插入的记录
            address_id_query = """
                SELECT id FROM ccuser_extend
                WHERE ccuser_id = :ccuser_id AND contact_phone = :contact_phone
                ORDER BY id DESC LIMIT 1
            """
            address_id_result = await query_db.execute(text(address_id_query), {
                "ccuser_id": str(customer_id),
                "contact_phone": customer_info.phone
            })
            address_id_row = address_id_result.fetchone()
            address_id = address_id_row._mapping.get("id") if address_id_row else 0

            if not address_id:
                raise BusinessException(message="创建地址后查询失败", code=1007)

            logger.info(f"创建客户地址成功: {address_id}")
            return address_id

        except Exception as e:
            logger.error(f"创建客户地址失败: {str(e)}")
            raise BusinessException(message="创建地址失败", code=1007)

    @classmethod
    async def _build_proxy_order_data(
        cls,
        order_data,
        store_info: Dict[str, Any],
        product_info: Dict[str, Any],
        sku_info: Dict[str, Any],
        current_user,
        customer_info: Dict[str, Any],
        address_id: int,
        user_store_uuid: str
    ) -> Dict[str, Any]:
        """构建代客下单数据"""
        try:
            # 获取用户信息
            user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'id', None)

            # 拼接服务日期时间
            service_datetime = f"{order_data.order_info.service_date} {order_data.order_info.service_time}"

            return {
                # 基础订单信息
                "store_id": store_info['id'],
                "store_name": store_info.get('name', ''),  # 使用name字段
                "store_uuid": user_store_uuid,
                "product_id": product_info['id'],
                "product_name": product_info.get('product_name', ''),
                "product_type_name": product_info.get('product_type_name', '服务'),
                "product_uuid": order_data.product_info.product_uuid,
                "sku_id": order_data.product_info.sku_id,
                "amount": float(order_data.order_info.amount),
                "buy_num": float(order_data.order_info.buy_num),
                "service_date": service_datetime,  # 完整的日期时间格式
                "service_time": order_data.order_info.service_time,  # 保留时间字段用于其他用途
                "pay_type": order_data.order_info.pay_type,

                # 客户信息 - 使用处理后的客户数据
                "ccuser_id": customer_info['id'],
                "customer_name": customer_info['name'],
                "customer_phone": customer_info['mobile'],
                "address_id": address_id,  # 关键：使用创建的地址ID

                # 地址信息（保留原有字段用于兼容）
                "service_address": order_data.address_info.address,
                "door_number": order_data.address_info.door_number or "",
                "longitude": float(order_data.address_info.longitude),
                "latitude": float(order_data.address_info.latitude),
                "city": order_data.address_info.city or "",
                "province": order_data.address_info.province or "",
                "district": order_data.address_info.district or "",

                # 备注信息
                "service_remark": order_data.remark_info.service_remark if order_data.remark_info else "",
                "customer_note": order_data.remark_info.customer_note if order_data.remark_info else "",
                "after_sale_remark": order_data.remark_info.after_sale_remark if order_data.remark_info else "",

                # 扩展业务信息
                "sales_attribution": order_data.business_info.sales_attribution if order_data.business_info else "",
                "sales_attribution_staff_id": getattr(order_data.business_info, 'sales_attribution_staff_id', '') if order_data.business_info else "",
                "sales_commission": float(order_data.business_info.sales_commission) if order_data.business_info and order_data.business_info.sales_commission else 0,
                "channel_code": order_data.business_info.channel_code if order_data.business_info else "",
                "order_source": order_data.business_info.order_source if order_data.business_info else "",
                "area": order_data.business_info.area if order_data.business_info else "",
                "house_type": order_data.business_info.house_type if order_data.business_info else "",

                # 系统信息
                "create_by": current_user.user.id,  # 使用用户ID而不是UUID
                "create_by_uuid": user_id,  # 保留UUID用于其他用途
                "operator_name": current_user.user.name or "代客下单操作员",  # 操作员姓名
                "order_source_type": "proxy",
                "order_status": 10,  # 已接单
                "create_time": datetime.datetime.now()
            }
        except Exception as e:
            logger.error(f"构建订单数据失败: {str(e)}")
            raise BusinessException(message="订单数据构建失败", code=1009)

    @classmethod
    async def _process_balance_payment_for_proxy_order(
        cls,
        query_db: AsyncSession,
        current_user,
        amount: float,
        order_number: str = "",
        customer_name: str = "",
        customer_mobile: str = ""
    ) -> Dict[str, Any]:
        """处理代客下单的余额支付"""
        try:
            # 获取用户公司ID
            user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
            user_name = getattr(current_user.user, 'name', '') or getattr(current_user.user, 'user_name', '')

            if not user_id:
                raise BusinessException(message="用户信息获取失败")

            # 调用余额支付逻辑（不提交事务，等待订单创建完成后一起提交）
            payment_result = await cls._process_balance_deduction(
                query_db, user_id, user_name, amount, order_number, customer_name, customer_mobile
            )

            logger.info(f"代客下单余额支付成功: {payment_result}")
            return payment_result

        except Exception as e:
            logger.error(f"代客下单余额支付失败: {str(e)}")
            raise BusinessException(message=f"余额支付失败: {str(e)}")

    @classmethod
    async def _process_balance_deduction(
        cls,
        query_db: AsyncSession,
        user_id: str,
        user_name: str,
        amount: float,
        order_number: str = "",
        customer_name: str = "",
        customer_mobile: str = "",
        payment_type: str = "余额"
    ) -> Dict[str, Any]:
        """处理余额扣费（不提交事务）"""
        try:
            # 获取用户公司ID
            from sqlalchemy import text

            user_query = """
                SELECT company_id FROM internal_user
                WHERE uuid = :user_id
                LIMIT 1
            """
            user_result = await query_db.execute(text(user_query), {"user_id": user_id})
            user_row = user_result.fetchone()

            if not user_row:
                raise BusinessException(message="用户信息不存在")

            company_id = user_row._mapping.get("company_id")
            if not company_id:
                raise BusinessException(message="用户未关联公司")

            # 检查余额是否充足
            balance_query = """
                SELECT balance FROM company
                WHERE id = :company_id AND is_delete = '0'
            """
            balance_result = await query_db.execute(text(balance_query), {"company_id": company_id})
            balance_row = balance_result.fetchone()

            if not balance_row:
                raise BusinessException(message="公司信息不存在")

            current_balance = balance_row._mapping.get("balance", 0)

            # 确保数据类型一致，都转换为 Decimal
            from decimal import Decimal
            current_balance = Decimal(str(current_balance)) if current_balance else Decimal('0')
            amount_decimal = Decimal(str(amount))

            if current_balance < amount_decimal:
                raise BusinessException(message=f"余额不足，当前余额: {current_balance}, 需要: {amount_decimal}")

            # 生成交易流水号
            import time
            import uuid
            timestamp = int(time.time() * 1000)
            transaction_no = f"TXN{timestamp}{str(uuid.uuid4()).replace('-', '')[:8].upper()}"

            # 扣除余额（不提交事务）
            new_balance = current_balance - amount_decimal

            update_balance_query = text("""
                UPDATE company
                SET balance = :new_balance
                WHERE id = :company_id
            """)

            await query_db.execute(update_balance_query, {
                "new_balance": new_balance,
                "company_id": company_id
            })

            # 插入交易流水记录（不提交事务）
            insert_sql = """
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_status, transaction_time,
                    created_by, created_at
                ) VALUES (
                    :company_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :operator_id, :operator_name,
                    :description, :remark, :transaction_status, NOW(),
                    :created_by, NOW()
                )
            """

            # 构建描述和备注信息
            if order_number:
                description = f"代客下单（{payment_type}），订单号：{order_number}"
            else:
                description = f"代客下单（{payment_type}），订单号：待生成"

            # 构建客户信息
            if customer_name and customer_mobile:
                remark = f"余额支付，客户：{customer_name}，手机：{customer_mobile}"
            elif customer_name:
                remark = f"余额支付，客户：{customer_name}，手机：未提供"
            elif customer_mobile:
                remark = f"余额支付，客户：未提供，手机：{customer_mobile}"
            else:
                remark = "余额支付，客户：未知客户，手机：未提供"

            insert_params = {
                "company_uuid": company_id,
                "transaction_no": transaction_no,
                "business_type": "PROXY_ORDER",
                "business_type_name": "代客下单",
                "transaction_type": 2,  # 2=支出
                "amount": float(amount_decimal),  # 转换为 float 存储
                "balance_before": float(current_balance),  # 转换为 float 存储
                "balance_after": float(new_balance),  # 转换为 float 存储
                "related_order_no": order_number if order_number else transaction_no,
                "pay_type": "BALANCE",
                "operator_id": user_id,
                "operator_name": user_name,
                "description": description,
                "remark": remark,
                "transaction_status": "SUCCESS",
                "created_by": user_id
            }

            await query_db.execute(text(insert_sql), insert_params)

            logger.info(f"余额扣费成功: {current_balance} -> {new_balance}, 扣费金额: {amount_decimal}")

            return {
                "transaction_no": transaction_no,
                "amount": float(amount_decimal),
                "balance_before": float(current_balance),
                "balance_after": float(new_balance),
                "status": "SUCCESS",
                "message": "支付成功"
            }

        except Exception as e:
            logger.error(f"余额扣费失败: {str(e)}")
            raise BusinessException(message=f"余额扣费失败: {str(e)}")

    @classmethod
    async def _update_order_payment_status_after_balance_payment(
        cls,
        query_db: AsyncSession,
        order_number: str,
        transaction_no: str
    ):
        """更新订单支付状态（余额支付后）"""
        try:
            # 更新订单支付记录状态
            update_payment_sql = """
                UPDATE order_payments
                SET pay_status = 1, transaction_id = :transaction_no, pay_time = NOW()
                WHERE order_number = :order_number
            """

            await query_db.execute(text(update_payment_sql), {
                "order_number": order_number,
                "transaction_no": transaction_no
            })

            logger.info(f"更新订单支付状态成功: {order_number}")

        except Exception as e:
            logger.error(f"更新订单支付状态失败: {str(e)}")
            # 不抛出异常，避免影响主流程

    @classmethod
    def _generate_proxy_order_number(cls) -> str:
        """生成代客下单订单号"""
        import time
        import random
        timestamp = int(time.time())
        random_num = random.randint(100000, 999999)
        return f"PX{timestamp}{random_num}"



    @classmethod
    async def _create_proxy_order_record(cls, query_db: AsyncSession, order_data: Dict[str, Any], payment_result: Optional[Dict[str, Any]] = None, order_number: Optional[str] = None) -> Dict[str, Any]:
        """创建代客下单记录"""
        try:
            # 使用传入的订单号，如果没有则生成新的
            if not order_number:
                order_number = cls._generate_proxy_order_number()

            # 1. 插入订单记录
            order_insert_sql = """
                INSERT INTO `order` (
                    order_number, user_id, store_id, store_name, store_uuid, product_id, product_name, product_type,
                    product_type_name, service_address, address_id, service_phone, order_status,
                    order_status_name, service_date, create_time, update_time, buy_num,
                    pay_actual, source, total_pay_actual, service_hour, product_sku_id, service_type_name,
                    sale_user_uuid, sale_user_commission
                ) VALUES (
                    :order_number, :user_id, :store_id, :store_name, :store_uuid, :product_id, :product_name, :product_type,
                    :product_type_name, :service_address, :address_id, :service_phone, :order_status,
                    :order_status_name, :service_date, NOW(), NOW(), :buy_num,
                    :pay_actual, :source, :total_pay_actual, :service_hour, :product_sku_id, :service_type_name,
                    :sale_user_uuid, :sale_user_commission
                )
            """

            await query_db.execute(text(order_insert_sql), {
                "order_number": order_number,
                "user_id": order_data.get('ccuser_id'),
                "store_id": order_data.get('store_id'),
                "store_name": order_data.get('store_name', ''),
                "store_uuid": order_data.get('store_uuid'),
                "product_id": order_data.get('product_id'),
                "product_name": order_data.get('product_name', ''),
                "product_type": 1,  # 服务类
                "product_type_name": order_data.get('product_type_name', '服务'),
                "service_address": order_data.get('service_address'),
                "address_id": order_data.get('address_id'),
                "service_phone": order_data.get('customer_phone', ''),
                "order_status": 10,  # 已接单
                "order_status_name": "已接单",
                "service_date": order_data.get('service_date'),  # 已经是完整格式
                "buy_num": order_data.get('buy_num', 1),
                "pay_actual": order_data.get('amount', 0),  # 代客下单默认已支付
                "source": "proxy",  # 代客下单标识
                "total_pay_actual": order_data.get('amount', 0),
                "service_hour": str(order_data.get('service_time', '')),
                "product_sku_id": order_data.get('sku_id'),
                "service_type_name": order_data.get('service_type_name', ''),
                "sale_user_uuid": order_data.get('sales_attribution_staff_id', ''),  # 销售人员UUID
                "sale_user_commission": order_data.get('sales_commission', 0)  # 销售提成
            })

            # 获取插入的订单ID - 查询刚插入的记录
            order_id_query = """
                SELECT id FROM `order` WHERE order_number = :order_number LIMIT 1
            """
            order_id_result = await query_db.execute(text(order_id_query), {"order_number": order_number})
            order_id_row = order_id_result.fetchone()
            order_id = order_id_row._mapping.get("id") if order_id_row else None

            # 2. 插入支付流水记录
            await cls._create_payment_record(query_db, order_number, order_data, payment_result)

            # 3. 插入订单日志
            await cls._create_order_logs(query_db, order_number, order_data)

            # 不在这里commit，由主方法统一管理事务
            return {
                "order_id": order_id,
                "order_number": order_number
            }
        except Exception as e:
            logger.error(f"创建订单记录失败: {str(e)}")
            raise BusinessException(message="创建订单记录失败", code=1010)

    @classmethod
    async def _create_payment_record(cls, query_db: AsyncSession, order_number: str, order_data: Dict[str, Any], payment_result: Optional[Dict[str, Any]] = None):
        """创建支付流水记录"""
        try:
            # 根据支付方式确定支付类型和名称
            pay_type = order_data.get('pay_type', '106')  # 默认现金支付

            if pay_type == 'BALANCE':
                # 余额支付
                pay_type_code = 111  # 余额支付类型代码
                pay_type_name = '余额支付'
                if payment_result:
                    # 有支付结果说明已经扣费成功
                    pay_status = 1  # 已支付状态
                    transaction_no = payment_result.get('transaction_no', '')
                else:
                    # 没有支付结果说明是待付款
                    pay_status = 0  # 待付款状态
                    transaction_no = ''
            else:
                # 现金支付
                pay_type_code = 1
                pay_type_name = '现金支付'
                pay_status = 0  # 待付款状态
                transaction_no = ''

            payment_insert_sql = """
                INSERT INTO order_payments (
                    order_number, pay_type, pay_type_name, pay_time, pay_status,
                    pay_actual, pay_money, create_time, transaction_id
                ) VALUES (
                    :order_number, :pay_type, :pay_type_name, NOW(), :pay_status,
                    :pay_actual, :pay_money, NOW(), :transaction_id
                )
            """

            await query_db.execute(text(payment_insert_sql), {
                "order_number": order_number,
                "pay_type": pay_type_code,
                "pay_type_name": pay_type_name,
                "pay_status": pay_status,
                "pay_actual": order_data.get('amount', 0),
                "pay_money": order_data.get('amount', 0),
                "transaction_id": transaction_no
            })

            logger.info(f"创建支付流水记录成功: {order_number}, 支付方式: {pay_type_name}")

        except Exception as e:
            logger.error(f"创建支付流水记录失败: {str(e)}")
            # 支付记录创建失败不影响主流程

    @classmethod
    async def _create_order_logs(cls, query_db: AsyncSession, order_number: str, order_data: Dict[str, Any]):
        """创建订单日志记录"""
        try:
            # 1. 插入订单创建日志
            create_log_sql = """
                INSERT INTO order_logs (
                    order_number, operation_type, operation_desc,
                    operator_id, operator_name, operation_time, create_time
                ) VALUES (
                    :order_number, 1, '代客下单创建',
                    :operator_id, :operator_name, NOW(), NOW()
                )
            """

            await query_db.execute(text(create_log_sql), {
                "order_number": order_number,
                "operator_id": order_data.get('create_by', ''),
                "operator_name": order_data.get('operator_name') or '代客下单操作员'
            })

            # 2. 插入支付日志
            payment_log_sql = """
                INSERT INTO order_logs (
                    order_number, operation_type, operation_desc,
                    operator_id, operator_name, operation_time, create_time
                ) VALUES (
                    :order_number, 2, :operation_desc,
                    :operator_id, :operator_name, NOW(), NOW()
                )
            """

            # 格式化支付金额
            amount = float(order_data.get('amount', 0))
            formatted_amount = "{:.2f}".format(amount)
            operation_desc = f"现金支付, 金额：¥{formatted_amount}元"

            await query_db.execute(text(payment_log_sql), {
                "order_number": order_number,
                "operation_desc": operation_desc,
                "operator_id": order_data.get('create_by', ''),
                "operator_name": order_data.get('operator_name') or '代客下单操作员'
            })

            logger.info(f"创建订单日志记录成功: {order_number}")

        except Exception as e:
            logger.error(f"创建订单日志记录失败: {str(e)}")
            # 日志记录创建失败不影响主流程，不抛出异常

    @classmethod
    async def _create_proxy_related_records(
        cls,
        query_db: AsyncSession,
        order_id: int,
        order_data
    ):
        """创建代客下单相关记录"""
        try:
            # 这里可以添加创建相关记录的逻辑
            # 比如订单扩展信息、备注记录等
            logger.info(f"代客下单相关记录创建完成，订单ID: {order_id}")
        except Exception as e:
            logger.error(f"创建相关记录失败: {str(e)}")
            # 相关记录创建失败不影响主流程



    # ==================== 订单修改业务方法 ====================

    @classmethod
    async def update_order_amount_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        new_amount: float,
        store_uuid: str
    ) -> Dict[str, Any]:
        """
        修改订单金额业务服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param new_amount: 新的订单金额
        :param store_uuid: 商家门店UUID
        :return: 修改结果
        """
        try:
            # 参数验证
            if not order_number or not order_number.strip():
                raise ValidationException(message="订单编号不能为空")

            if new_amount <= 0:
                raise ValidationException(message="订单金额必须大于0")

            if not store_uuid:
                raise ValidationException(message="商家门店信息不能为空")

            # 调用DAO层修改订单金额
            result = await OrderDao.update_order_amount(
                query_db,
                order_number.strip(),
                new_amount,
                store_uuid
            )

            logger.info(f"订单金额修改成功，订单号: {order_number}, 新金额: {new_amount}")
            return result

        except ValidationException as e:
            logger.error(f"订单金额修改验证异常: {e.message}")
            raise e
        except QueryException as e:
            logger.error(f"订单金额修改查询异常: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"订单金额修改业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"订单金额修改服务异常: {str(e)}")
            raise BusinessException(message=f"修改订单金额失败: {str(e)}")

    @classmethod
    async def update_order_time_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        new_service_date: str,
        store_uuid: str
    ) -> Dict[str, Any]:
        """
        修改订单时间业务服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param new_service_date: 新的服务时间
        :param store_uuid: 商家门店UUID
        :return: 修改结果
        """
        try:
            # 参数验证
            if not order_number or not order_number.strip():
                raise ValidationException(message="订单编号不能为空")

            if not new_service_date or not new_service_date.strip():
                raise ValidationException(message="服务时间不能为空")

            if not store_uuid:
                raise ValidationException(message="商家门店信息不能为空")

            # 验证时间格式
            import re
            from datetime import datetime
            pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:00:00$'
            if not re.match(pattern, new_service_date.strip()):
                raise ValidationException(message="服务时间格式必须为：YYYY-MM-DD HH:00:00")

            # 验证时间是否为未来时间
            try:
                service_datetime = datetime.strptime(new_service_date.strip(), '%Y-%m-%d %H:%M:%S')
                if service_datetime <= datetime.now():
                    raise ValidationException(message="服务时间必须为未来时间")
            except ValueError:
                raise ValidationException(message="服务时间格式不正确")

            # 直接调用DAO层修改订单时间
            result = await OrderDao.update_order_time(
                query_db,
                order_number.strip(),
                new_service_date.strip(),
                store_uuid
            )



            logger.info(f"订单时间修改成功，订单号: {order_number}, 新时间: {new_service_date}")
            return result

        except ValidationException as e:
            logger.error(f"订单时间修改验证异常: {e.message}")
            raise e
        except QueryException as e:
            logger.error(f"订单时间修改查询异常: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"订单时间修改业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"订单时间修改服务异常: {str(e)}")
            raise BusinessException(message=f"修改订单时间失败: {str(e)}")

    @classmethod
    async def _get_customer_balance(cls, query_db: AsyncSession, user_id: str) -> Optional[Dict[str, Any]]:
        """查询客户余额信息"""
        try:
            query = text("""
                SELECT uuid, name, mobile, amount
                FROM ccuser
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            result = await query_db.execute(query, {"user_id": user_id})
            row = result.fetchone()

            if row:
                return {
                    "uuid": row._mapping.get('uuid'),
                    "name": row._mapping.get('name', ''),
                    "mobile": row._mapping.get('mobile', ''),
                    "amount": row._mapping.get('amount', 0)
                }
            return None

        except Exception as e:
            logger.error(f"查询客户余额失败: {str(e)}")
            raise BusinessException(message=f"查询客户余额失败: {str(e)}")

    @classmethod
    async def _create_cash_payment_transaction(
        cls,
        query_db: AsyncSession,
        customer_uuid: str,
        order_number: str,
        order_amount: float,
        customer_balance: float,
        operator_id: str,
        operator_name: str,
        customer_name: str,
        customer_mobile: str
    ) -> Dict[str, Any]:
        """创建现金支付流水记录"""
        try:
            import time
            import uuid

            # 生成交易流水号
            timestamp = int(time.time() * 1000)
            transaction_no = f"TXN{timestamp}{str(uuid.uuid4()).replace('-', '')[:8].upper()}"

            # 插入ccuser_transaction流水记录
            insert_sql = text("""
                INSERT INTO ccuser_transaction (
                    ccuser_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_time, created_by, created_at,
                    transaction_status
                ) VALUES (
                    :ccuser_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :operator_id, :operator_name,
                    :description, :remark, NOW(), :created_by, NOW(),
                    :transaction_status
                )
            """)

            # 构建流水记录参数
            transaction_params = {
                "ccuser_uuid": customer_uuid,
                "transaction_no": transaction_no,
                "business_type": "SERVICE_CONSUMPTION",
                "business_type_name": "服务消费",
                "transaction_type": 2,  # 支出类型
                "amount": 0.00,  # 现金支付不实际扣费
                "balance_before": customer_balance,
                "balance_after": customer_balance,  # 余额不变
                "related_order_no": order_number,
                "pay_type": "CASH",
                "operator_id": operator_id,
                "operator_name": operator_name,
                "description": f"现金支付，订单号：{order_number}",
                "remark": f"现金支付，客户：{customer_name}，手机：{customer_mobile}，订单金额：{order_amount:.2f}元",
                "created_by": operator_id,
                "transaction_status": "SUCCESS"
            }

            await query_db.execute(insert_sql, transaction_params)
            logger.info(f"插入现金支付流水记录完成: {transaction_no}")

            return {
                "transaction_no": transaction_no,
                "amount": 0.00,
                "balance_before": customer_balance,
                "balance_after": customer_balance,
                "status": "SUCCESS",
                "message": "现金支付流水记录创建成功"
            }

        except Exception as e:
            logger.error(f"创建现金支付流水记录失败: {str(e)}")
            raise BusinessException(message=f"创建现金支付流水记录失败: {str(e)}")

    @classmethod
    async def balance_payment_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        current_user
    ) -> Dict[str, Any]:
        """余额支付服务"""
        try:
            # 1. 查询订单信息
            order_info = await OrderDao.get_order_by_number(query_db, order_number)
            if not order_info:
                raise BusinessException(message="订单不存在")

            # 2. 检查订单支付状态
            pay_status = order_info.get('pay_status')
            if pay_status == 1:
                raise BusinessException(message="订单已支付，无需重复支付")

            # 3. 获取订单金额和客户ID
            pay_actual = order_info.get('pay_actual', 0) or order_info.get('pay_money', 0)
            order_amount = float(pay_actual) if pay_actual else 0.0
            if order_amount <= 0:
                raise BusinessException(message="订单金额异常")

            user_id = order_info.get('user_id')
            if not user_id:
                raise BusinessException(message="订单未关联客户信息")

            # 4. 查询客户余额信息
            customer_info = await cls._get_customer_balance(query_db, user_id)
            if not customer_info:
                raise BusinessException(message="客户信息不存在")

            customer_uuid = customer_info.get('uuid')
            # 处理amount字段为NULL的情况
            amount_value = customer_info.get('amount')
            if amount_value is None:
                amount_value = 0
            customer_balance = float(amount_value)
            customer_name = customer_info.get('name', '')
            customer_mobile = customer_info.get('mobile', '')

            if not customer_uuid:
                raise BusinessException(message="客户UUID获取失败")

            # 5. 验证客户余额是否足够
            if customer_balance < order_amount:
                raise BusinessException(message=f"客户余额不足，当前余额：{customer_balance:.2f}元，订单金额：{order_amount:.2f}元")

            # 6. 获取操作人信息
            operator_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
            operator_name = getattr(current_user.user, 'name', '') or getattr(current_user.user, 'user_name', '')

            if not operator_id:
                raise BusinessException(message="操作人信息获取失败")

            # 7. 计算扣费后余额
            new_balance = customer_balance - order_amount

            # 8. 更新客户余额
            await cls._update_customer_balance(query_db, user_id, new_balance)

            # 9. 插入余额支付流水记录
            transaction_result = await cls._create_balance_payment_transaction(
                query_db, customer_uuid, order_number, order_amount, customer_balance, new_balance,
                operator_id, operator_name, customer_name, customer_mobile
            )

            # 10. 插入订单支付记录
            await cls._create_order_payment_record(
                query_db, order_number, order_amount, transaction_result.get('transaction_no', ''), 'BALANCE'
            )

            # 11. 更新订单支付状态
            await cls._update_order_payment_status(query_db, order_number, transaction_result.get('transaction_no', ''))

            await query_db.commit()

            return {
                "order_number": order_number,
                "amount": order_amount,
                "transaction_id": transaction_result.get('transaction_no', ''),
                "balance_before": customer_balance,
                "balance_after": new_balance,
                "message": "余额支付成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"余额支付失败: {str(e)}")
            raise BusinessException(message=f"余额支付失败: {str(e)}")

    @classmethod
    async def order_qrcode_payment_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        current_user,
        request
    ) -> Dict[str, Any]:
        """订单扫码支付服务"""
        try:
            # 1. 查询订单信息
            order_info = await OrderDao.get_order_by_number(query_db, order_number)
            if not order_info:
                raise BusinessException(message="订单不存在")

            # 2. 检查订单支付状态
            pay_status = order_info.get('pay_status')
            if pay_status == 1:
                raise BusinessException(message="订单已支付，无需重复支付")
            elif pay_status == 2:
                raise BusinessException(message="订单支付失败，请联系客服")
            elif pay_status == 3:
                raise BusinessException(message="订单支付已取消")
            elif pay_status not in [0, None]:
                raise BusinessException(message=f"订单支付状态异常: {pay_status}")

            # 3. 获取订单金额
            pay_actual = order_info.get('pay_actual', 0) or order_info.get('pay_money', 0)
            order_amount = float(pay_actual) if pay_actual else 0.0
            if order_amount <= 0:
                raise BusinessException(message="订单金额异常")

            # 4. 获取当前用户信息
            current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
            if not current_user_id:
                logger.warning("当前用户ID获取失败")
                raise ValidationException(message="当前用户ID获取失败")

            logger.info(f"用户 {current_user_id} 发起订单扫码支付，订单号: {order_number}, 金额: {order_amount}")

            # 5. 调用专门的订单扫码支付服务
            from module_admin.service.payment_service import PaymentService
            user_id = order_info.get('user_id') or ''
            customer_uuid = order_info.get('customer_uuid') or ''

            payment_result = await PaymentService.create_order_qrcode_payment_service(
                query_db, order_number, user_id, customer_uuid, order_amount, request
            )

            return {
                "order_number": order_number,
                "payment_order_number": payment_result.get('payment_order_number'),
                "payUrl": payment_result.get('payUrl'),
                "amount": order_amount,
                "message": "创建订单扫码支付成功"
            }

        except BusinessException as e:
            raise e
        except Exception as e:
            logger.error(f"订单扫码支付失败: {str(e)}")
            raise BusinessException(message=f"订单扫码支付失败: {str(e)}")

    @classmethod
    async def _save_order_qrcode_payment_record(
        cls,
        query_db: AsyncSession,
        order_number: str,
        payment_order_number: str,
        amount: float,
        order_info: Dict[str, Any]
    ) -> None:
        """保存订单扫码支付关联记录"""
        try:
            # 在company_transaction表的备注中记录原订单信息
            # 这样支付成功后可以通过备注查询到关联的原订单

            # 更新company_transaction表，在备注中记录原订单信息
            update_sql = text("""
                UPDATE company_transaction
                SET remark = CONCAT(
                    COALESCE(remark, ''),
                    CASE WHEN remark IS NOT NULL AND remark != '' THEN ', ' ELSE '' END,
                    :order_info
                )
                WHERE related_order_no = :payment_order_number
                AND business_type = 'RECHARGE'
            """)

            order_info_str = f"关联订单号:{order_number},用户ID:{order_info.get('user_id')},客户UUID:{order_info.get('customer_uuid')}"

            await query_db.execute(update_sql, {
                "order_info": order_info_str,
                "payment_order_number": payment_order_number
            })

            logger.info(f"订单扫码支付关联记录保存成功 - 订单号: {order_number}, 支付订单号: {payment_order_number}, 金额: {amount}")

        except Exception as e:
            logger.error(f"保存订单扫码支付关联记录失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            logger.warning("关联记录保存失败，但不影响支付流程")

    @classmethod
    async def _update_customer_balance(cls, query_db: AsyncSession, user_id: str, new_balance: float) -> None:
        """更新客户余额"""
        try:
            update_sql = text("""
                UPDATE ccuser
                SET amount = :new_balance, updated_at = NOW()
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            await query_db.execute(update_sql, {
                "new_balance": new_balance,
                "user_id": user_id
            })

            logger.info(f"更新客户余额完成: user_id={user_id}, new_balance={new_balance}")

        except Exception as e:
            logger.error(f"更新客户余额失败: {str(e)}")
            raise BusinessException(message=f"更新客户余额失败: {str(e)}")

    @classmethod
    async def _create_balance_payment_transaction(
        cls,
        query_db: AsyncSession,
        customer_uuid: str,
        order_number: str,
        order_amount: float,
        balance_before: float,
        balance_after: float,
        operator_id: str,
        operator_name: str,
        customer_name: str,
        customer_mobile: str
    ) -> Dict[str, Any]:
        """创建余额支付流水记录"""
        try:
            import time
            import uuid

            # 生成交易流水号
            timestamp = int(time.time() * 1000)
            transaction_no = f"BAL{timestamp}{str(uuid.uuid4()).replace('-', '')[:8].upper()}"

            # 插入ccuser_transaction流水记录
            insert_sql = text("""
                INSERT INTO ccuser_transaction (
                    ccuser_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_time, created_by, created_at,
                    transaction_status
                ) VALUES (
                    :ccuser_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :operator_id, :operator_name,
                    :description, :remark, NOW(), :created_by, NOW(),
                    :transaction_status
                )
            """)

            # 构建流水记录参数
            transaction_params = {
                "ccuser_uuid": customer_uuid,
                "transaction_no": transaction_no,
                "business_type": "SERVICE_CONSUMPTION",
                "business_type_name": "服务消费",
                "transaction_type": 2,  # 支出类型
                "amount": order_amount,  # 余额支付实际扣费
                "balance_before": balance_before,
                "balance_after": balance_after,
                "related_order_no": order_number,
                "pay_type": "BALANCE",
                "operator_id": operator_id,
                "operator_name": operator_name,
                "description": f"余额支付，订单号：{order_number}",
                "remark": f"余额支付，客户：{customer_name}，手机：{customer_mobile}，扣费金额：{order_amount:.2f}元",
                "created_by": operator_id,
                "transaction_status": "SUCCESS"
            }

            await query_db.execute(insert_sql, transaction_params)
            logger.info(f"插入余额支付流水记录完成: {transaction_no}")

            return {
                "transaction_no": transaction_no,
                "amount": order_amount,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "status": "SUCCESS",
                "message": "余额支付流水记录创建成功"
            }

        except Exception as e:
            logger.error(f"创建余额支付流水记录失败: {str(e)}")
            raise BusinessException(message=f"创建余额支付流水记录失败: {str(e)}")

    @classmethod
    async def _create_order_payment_record(
        cls,
        query_db: AsyncSession,
        order_number: str,
        payment_amount: float,
        transaction_id: str,
        payment_method: str
    ) -> None:
        """创建订单支付记录"""
        try:
            import uuid

            # 生成支付记录ID
            payment_id = str(uuid.uuid4())

            # 插入order_payments记录 - 使用实际的表字段结构
            insert_sql = text("""
                INSERT INTO order_payments (
                    order_number, pay_type, pay_type_name, pay_money,
                    pay_actual, pay_status, pay_time, transaction_id,
                    create_time, update_time
                ) VALUES (
                    :order_number, :pay_type, :pay_type_name, :pay_money,
                    :pay_actual, :pay_status, NOW(), :transaction_id,
                    NOW(), NOW()
                )
            """)

            # 根据支付方式设置pay_type代码
            pay_type_code = 111 if payment_method == 'BALANCE' else 106  # 111=余额支付, 106=现金支付
            pay_type_name = '余额支付' if payment_method == 'BALANCE' else '现金支付'

            # 构建支付记录参数
            payment_params = {
                "order_number": order_number,
                "pay_type": pay_type_code,
                "pay_type_name": pay_type_name,
                "pay_money": payment_amount,
                "pay_actual": payment_amount,
                "pay_status": 1,  # 1表示支付成功
                "transaction_id": transaction_id
            }

            await query_db.execute(insert_sql, payment_params)
            logger.info(f"插入订单支付记录完成: order_number={order_number}, payment_method={payment_method}")

        except Exception as e:
            logger.error(f"创建订单支付记录失败: {str(e)}")
            raise BusinessException(message=f"创建订单支付记录失败: {str(e)}")

    @classmethod
    async def get_order_share_detail_service(cls, query_db: AsyncSession, order_number: str) -> Dict[str, Any]:
        """
        获取订单分享详情服务（无需token验证）

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :return: 订单分享详情
        """
        try:
            logger.info(f"开始获取订单分享详情: {order_number}")

            # 查询订单基本信息，包括预设佣金
            order_sql = text("""
                SELECT
                    order_number,
                    product_name,
                    service_date,
                    service_hour,
                    service_address,
                    total_pay_actual,
                    service_remark,
                    store_id,
                    store_uuid,
                    store_name,
                    user_id,
                    order_status,
                    preset_commission
                FROM `order`
                WHERE order_number = :order_number
                AND order_status NOT IN (99, 0)
            """)

            result = await query_db.execute(order_sql, {"order_number": order_number})
            order_info = result.fetchone()

            if not order_info:
                raise ResourceNotFoundException(message=f"订单不存在或已取消: {order_number}")

            # 转换为字典
            order_dict = {
                'order_number': order_info[0],
                'product_name': order_info[1],
                'service_date': str(order_info[2]) if order_info[2] else None,
                'service_hour': str(order_info[3]) if order_info[3] else None,
                'service_address': order_info[4],
                'service_address_masked': cls._mask_service_address(order_info[4]),
                'total_pay_actual': str(order_info[5]) if order_info[5] else '0',
                'service_remark': order_info[6],
                'store_id': order_info[7],
                'store_uuid': order_info[8],
                'store_name': order_info[9],
                'user_id': order_info[10],
                'order_status': order_info[11],
                'preset_commission': float(order_info[12]) if order_info[12] else 0,
                'order_status_name': cls._get_status_display_name(order_info[11]),
                'can_accept': order_info[11] == 10  # 只有已接单状态可以分享接单
            }

            # 查询客户信息
            customer_info = None
            if order_dict.get('user_id'):
                customer_sql = text("""
                    SELECT name, mobile, headimg
                    FROM ccuser
                    WHERE id = :user_id
                """)

                customer_result = await query_db.execute(customer_sql, {"user_id": order_dict['user_id']})
                customer_row = customer_result.fetchone()

                if customer_row:
                    customer_info = {
                        'name': customer_row[0],
                        'mobile': customer_row[1],
                        'headimg': customer_row[2]
                    }
                    # 对客户信息进行隐私保护
                    customer_info = cls._mask_customer_info(customer_info)

            # 构建返回数据
            share_detail = {
                "order_info": order_dict,
                "customer_info": customer_info
            }

            logger.info(f"获取订单分享详情成功: {order_number}")
            return share_detail

        except ResourceNotFoundException as e:
            logger.error(f"订单分享详情资源不存在: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"获取订单分享详情失败: {str(e)}")
            raise BusinessException(message=f"获取订单分享详情失败: {str(e)}")

    @staticmethod
    def _mask_service_address(address: str) -> str:
        """
        隐藏服务地址的详细信息，只保留到区级别

        :param address: 完整地址
        :return: 隐藏后的地址
        """
        if not address:
            return ""

        import re

        # 匹配省市区的正则表达式
        pattern = r'(.*?省)?(.*?市)?(.*?区|.*?县)'
        match = re.match(pattern, address)

        if match:
            province = match.group(1) or ""
            city = match.group(2) or ""
            district = match.group(3) or ""
            return f"{province}{city}{district}"
        else:
            # 如果无法匹配，返回前20个字符
            return address[:20] + "..." if len(address) > 20 else address

    @staticmethod
    def _get_status_display_name(status: int) -> str:
        """
        获取订单状态名称

        :param status: 状态码
        :return: 状态名称
        """
        status_map = {
            10: "已接单",
            20: "派单待确认",
            30: "拒绝接单",
            40: "已派单",
            50: "执行中",
            60: "开始服务",
            70: "服务结束",
            80: "已完成",
            99: "已取消"
        }
        return status_map.get(status, "未知状态")

    @staticmethod
    def _mask_customer_info(customer_info: dict) -> dict:
        """
        隐藏客户信息的敏感数据

        :param customer_info: 客户信息
        :return: 隐藏后的客户信息
        """
        if not customer_info:
            return {}

        masked_info = customer_info.copy()

        # 隐藏姓名：只显示姓氏
        if 'name' in masked_info and masked_info['name']:
            name = masked_info['name']
            if len(name) > 1:
                masked_info['name'] = name[0] + '*' * (len(name) - 1)

        # 隐藏手机号：只显示前3位
        if 'mobile' in masked_info and masked_info['mobile']:
            mobile = masked_info['mobile']
            if len(mobile) >= 3:
                masked_info['mobile'] = mobile[:3] + '*' * (len(mobile) - 3)

        return masked_info

    @classmethod
    async def check_staff_exists_service(cls, query_db: AsyncSession, mobile: str, order_number: Optional[str] = None) -> Dict[str, Any]:
        """
        检查员工是否存在服务

        :param query_db: 数据库会话
        :param mobile: 手机号
        :param order_number: 订单号（可选，用于检查特定门店）
        :return: 员工存在状态
        """
        try:
            logger.info(f"[DEBUG] 开始检查员工是否存在: {mobile}, 订单号: {order_number}")

            # 验证手机号格式
            logger.info(f"[DEBUG] 验证手机号格式: {mobile}, 长度: {len(mobile) if mobile else 0}")
            if not mobile:
                logger.error(f"[DEBUG] 手机号为空")
                raise ValidationException(message="手机号不能为空")
            
            # 更宽松的手机号验证：支持11位数字
            if not mobile.isdigit() or len(mobile) != 11:
                logger.error(f"[DEBUG] 手机号格式验证失败: {mobile}, 长度: {len(mobile)}, 是否全数字: {mobile.isdigit()}")
                raise ValidationException(message="手机号格式不正确，请输入11位数字")
            
            logger.info(f"[DEBUG] 手机号格式验证通过")

            # 如果提供了订单号，获取订单门店信息
            order_store_uuid = None
            if order_number:
                logger.info(f"[DEBUG] 开始查询订单门店信息: {order_number}")
                try:
                    order_sql = text("""
                        SELECT store_uuid, store_name
                        FROM `order`
                        WHERE order_number = :order_number
                        LIMIT 1
                    """)
                    logger.info(f"[DEBUG] 执行订单查询SQL: {order_sql}")
                    order_result = await query_db.execute(order_sql, {"order_number": order_number})
                    order_info = order_result.fetchone()
                    logger.info(f"[DEBUG] 订单查询结果: {order_info}")
                    if order_info:
                        order_store_uuid = order_info.store_uuid
                        logger.info(f"[DEBUG] 获取到订单门店UUID: {order_store_uuid}")
                    else:
                        logger.warning(f"[DEBUG] 未找到订单: {order_number}")
                except Exception as e:
                    logger.error(f"[DEBUG] 查询订单信息异常: {str(e)}")
                    raise

            # 查询员工在所有门店的存在情况
            logger.info(f"[DEBUG] 开始查询员工信息: {mobile}")
            try:
                staff_sql = text("""
                    SELECT
                        uuid,
                        real_name,
                        mobile,
                        store_id,
                        store_uuid,
                        store_name,
                        wx_openid,
                        status
                    FROM service_staff
                    WHERE mobile = :mobile
                    AND status = 1
                    ORDER BY create_time DESC
                """)
                logger.info(f"[DEBUG] 执行员工查询SQL: {staff_sql}")
                result = await query_db.execute(staff_sql, {"mobile": mobile})
                staff_records = result.fetchall()
                logger.info(f"[DEBUG] 员工查询结果数量: {len(staff_records)}")
                
                for i, record in enumerate(staff_records):
                    logger.info(f"[DEBUG] 员工记录{i}: uuid={record.uuid}, name={record.real_name}, store_uuid={record.store_uuid}")
            except Exception as e:
                logger.error(f"[DEBUG] 查询员工信息异常: {str(e)}")
                raise

            # 分析员工存在情况
            logger.info(f"[DEBUG] 开始分析员工存在情况")
            exists_in_order_store = False
            exists_in_other_store = False
            staff_data = None

            if staff_records:
                logger.info(f"[DEBUG] 找到员工记录，开始分析")
                staff_data = dict(staff_records[0]._mapping)  # 取最新的员工记录
                logger.info(f"[DEBUG] 默认员工数据: {staff_data}")

                for record in staff_records:
                    logger.info(f"[DEBUG] 检查员工记录: store_uuid={record.store_uuid}, order_store_uuid={order_store_uuid}")
                    if order_store_uuid and record.store_uuid == order_store_uuid:
                        exists_in_order_store = True
                        staff_data = dict(record._mapping)  # 优先使用订单门店的员工记录
                        logger.info(f"[DEBUG] 员工在订单门店存在")
                    else:
                        exists_in_other_store = True
                        logger.info(f"[DEBUG] 员工在其他门店存在")
            else:
                logger.info(f"[DEBUG] 未找到员工记录")

            logger.info(f"[DEBUG] 员工存在检查完成: {mobile}, 订单门店存在: {exists_in_order_store}, 其他门店存在: {exists_in_other_store}")

            result_data = {
                "exists": exists_in_order_store or exists_in_other_store,  # 兼容旧格式
                "exists_in_order_store": exists_in_order_store,
                "exists_in_other_store": exists_in_other_store,
                "staff_info": staff_data
            }
            logger.info(f"[DEBUG] 返回结果: {result_data}")
            return result_data

        except ValidationException as e:
            logger.error(f"[DEBUG] 验证异常: {e.message}")
            raise
        except Exception as e:
            logger.error(f"[DEBUG] 检查员工是否存在异常: {str(e)}")
            logger.error(f"[DEBUG] 异常类型: {type(e).__name__}")
            logger.error(f"[DEBUG] 异常详情: {repr(e)}")
            import traceback
            logger.error(f"[DEBUG] 异常堆栈: {traceback.format_exc()}")
            raise QueryException(message="检查员工状态失败")

    @classmethod
    async def copy_staff_to_store_service(cls, query_db: AsyncSession, mobile: str, order_number: str, openid: str) -> Dict[str, Any]:
        """
        复制员工到指定门店服务

        :param query_db: 数据库会话
        :param mobile: 手机号
        :param order_number: 订单号（用于获取目标门店信息）
        :param openid: 微信openid
        :return: 复制结果
        """
        try:
            logger.info(f"开始复制员工到门店: {mobile}, 订单号: {order_number}")

            # 验证手机号格式
            if not mobile or len(mobile) != 11:
                raise ValidationException(message="手机号格式不正确")

            if not order_number:
                raise ValidationException(message="订单号不能为空")

            # 获取订单门店信息
            order_sql = text("""
                SELECT store_id, store_uuid, store_name
                FROM `order`
                WHERE order_number = :order_number
                LIMIT 1
            """)
            order_result = await query_db.execute(order_sql, {"order_number": order_number})
            order_info = order_result.fetchone()

            if not order_info:
                raise ValidationException(message="订单不存在")

            target_store_id = order_info.store_id
            target_store_uuid = order_info.store_uuid
            target_store_name = order_info.store_name

            # 检查员工是否已在目标门店存在
            existing_staff_sql = text("""
                SELECT id FROM service_staff
                WHERE mobile = :mobile AND store_uuid = :store_uuid AND status = 1
                LIMIT 1
            """)
            existing_result = await query_db.execute(existing_staff_sql, {
                "mobile": mobile,
                "store_uuid": target_store_uuid
            })

            if existing_result.fetchone():
                return {
                    "success": True,
                    "message": "员工已在目标门店存在",
                    "staff_exists": True
                }

            # 获取员工在其他门店的信息
            source_staff_sql = text("""
                SELECT *
                FROM service_staff
                WHERE mobile = :mobile AND store_uuid != :store_uuid AND status = 1
                ORDER BY create_time DESC
                LIMIT 1
            """)
            source_result = await query_db.execute(source_staff_sql, {
                "mobile": mobile,
                "store_uuid": target_store_uuid
            })
            source_staff = source_result.fetchone()

            if not source_staff:
                raise ValidationException(message="未找到员工在其他门店的信息")

            # 获取目标门店的正确公司ID
            target_company_sql = text("""
                SELECT company_id FROM store
                WHERE id = :store_id
                LIMIT 1
            """)
            target_company_result = await query_db.execute(target_company_sql, {"store_id": target_store_id})
            target_company_row = target_company_result.fetchone()

            if not target_company_row:
                raise ValidationException(message="目标门店信息不存在")

            target_company_id = target_company_row.company_id

            # 复制员工信息到目标门店
            import uuid
            new_uuid = str(uuid.uuid4()).replace('-', '')[:10]

            copy_staff_sql = text("""
                INSERT INTO service_staff (
                    uuid, user_id, company_id, store_id, store_name, job_type, user_name, nick_name,
                    real_name, mobile, password, wx_openid, wx_unionid, wx_bind_time,
                    wx_official_openid, wx_official_unionid, wx_official_nickname, wx_official_avatar,
                    wx_official_bind_time, is_default_company, last_login_time, star_level, status,
                    work_type, is_delete, is_part_time_job, is_old, is_allow_rob, is_bind_wx,
                    sex, age, avatar, city_id, city, address, service_cnt, service_uv,
                    service_commission, sale_commission, own_user_id, own_user_name,
                    insurance_start_time, insurance_end_time, created_by, updated_by,
                    created_by_name, updated_by_name, store_uuid, invitation_code,
                    create_time, update_time
                ) VALUES (
                    :uuid, :user_id, :company_id, :store_id, :store_name, :job_type, :user_name, :nick_name,
                    :real_name, :mobile, :password, :wx_openid, :wx_unionid, :wx_bind_time,
                    :wx_official_openid, :wx_official_unionid, :wx_official_nickname, :wx_official_avatar,
                    :wx_official_bind_time, :is_default_company, :last_login_time, :star_level, :status,
                    :work_type, :is_delete, :is_part_time_job, :is_old, :is_allow_rob, :is_bind_wx,
                    :sex, :age, :avatar, :city_id, :city, :address, :service_cnt, :service_uv,
                    :service_commission, :sale_commission, :own_user_id, :own_user_name,
                    :insurance_start_time, :insurance_end_time, :created_by, :updated_by,
                    :created_by_name, :updated_by_name, :store_uuid, :invitation_code,
                    NOW(), NOW()
                )
            """)

            await query_db.execute(copy_staff_sql, {
                "uuid": new_uuid,
                "user_id": source_staff.user_id,
                "company_id": target_company_id,  # 使用目标门店的正确公司ID
                "store_id": str(target_store_id),  # 更新为目标门店ID
                "store_name": target_store_name,  # 更新为目标门店名称
                "job_type": source_staff.job_type,
                "user_name": source_staff.user_name,
                "nick_name": source_staff.nick_name,
                "real_name": source_staff.real_name,
                "mobile": source_staff.mobile,
                "password": source_staff.password,
                "wx_openid": openid,  # 使用当前用户的openid
                "wx_unionid": source_staff.wx_unionid,
                "wx_bind_time": source_staff.wx_bind_time,
                "wx_official_openid": source_staff.wx_official_openid,
                "wx_official_unionid": source_staff.wx_official_unionid,
                "wx_official_nickname": source_staff.wx_official_nickname,
                "wx_official_avatar": source_staff.wx_official_avatar,
                "wx_official_bind_time": source_staff.wx_official_bind_time,
                "is_default_company": source_staff.is_default_company,
                "last_login_time": source_staff.last_login_time,
                "star_level": source_staff.star_level,
                "status": source_staff.status,
                "work_type": source_staff.work_type,
                "is_delete": source_staff.is_delete,
                "is_part_time_job": source_staff.is_part_time_job,
                "is_old": source_staff.is_old,
                "is_allow_rob": source_staff.is_allow_rob,
                "is_bind_wx": source_staff.is_bind_wx,
                "sex": source_staff.sex,
                "age": source_staff.age,
                "avatar": source_staff.avatar,
                "city_id": source_staff.city_id,
                "city": source_staff.city,
                "address": source_staff.address,
                "service_cnt": source_staff.service_cnt,
                "service_uv": source_staff.service_uv,
                "service_commission": source_staff.service_commission,
                "sale_commission": source_staff.sale_commission,
                "own_user_id": source_staff.own_user_id,
                "own_user_name": source_staff.own_user_name,
                "insurance_start_time": source_staff.insurance_start_time,
                "insurance_end_time": source_staff.insurance_end_time,
                "created_by": source_staff.created_by,
                "updated_by": source_staff.updated_by,
                "created_by_name": source_staff.created_by_name,
                "updated_by_name": source_staff.updated_by_name,
                "store_uuid": target_store_uuid,  # 更新为目标门店UUID
                "invitation_code": source_staff.invitation_code
            })

            # 获取新插入的员工ID
            new_staff_result = await query_db.execute(text("SELECT LAST_INSERT_ID() as new_id"))
            new_staff_row = new_staff_result.fetchone()
            new_staff_id = new_staff_row.new_id

            # 复制员工扩展信息
            await cls._copy_staff_ext_info(query_db, source_staff.id, new_staff_id)

            # 复制订单相关的服务产品关联
            await cls._copy_order_related_products(query_db, order_number, new_staff_id, target_store_uuid)

            await query_db.commit()

            logger.info(f"员工完整信息复制成功: {mobile} -> {target_store_name} (包含扩展信息、订单服务产品)")

            return {
                "success": True,
                "message": f"员工信息已同步到{target_store_name}",
                "staff_exists": False,
                "new_staff_uuid": new_uuid,
                "new_staff_id": new_staff_id
            }

        except ValidationException:
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f"复制员工到门店异常: {str(e)}")
            raise QueryException(message="员工信息同步失败")

    @classmethod
    async def _copy_staff_ext_info(cls, query_db: AsyncSession, source_staff_id: int, new_staff_id: int):
        """
        复制员工扩展信息

        :param query_db: 数据库会话
        :param source_staff_id: 源员工ID
        :param new_staff_id: 新员工ID
        """
        try:
            logger.info(f"开始复制员工扩展信息: {source_staff_id} -> {new_staff_id}")

            # 查询源员工的扩展信息
            source_ext_sql = text("""
                SELECT *
                FROM service_staff_ext
                WHERE staff_id = :staff_id
                LIMIT 1
            """)
            source_ext_result = await query_db.execute(source_ext_sql, {"staff_id": source_staff_id})
            source_ext = source_ext_result.fetchone()

            if not source_ext:
                logger.info(f"源员工 {source_staff_id} 没有扩展信息，跳过复制")
                return

            # 复制扩展信息到新员工
            copy_ext_sql = text("""
                INSERT INTO service_staff_ext (
                    staff_id, id_number, birthday, id_number_md5, id_verify_status, id_verify_msg,
                    lng, lat, last_purpose_time, set_servicer_top, live_pos, id_card_people_photo,
                    id_card_address, id_card_valid_from, id_card_valid_to, id_card_long_term,
                    native_place, native_place_id, nation, nation_id, marriage_status,
                    education_background, height, weight, family_address, medical_history,
                    source, province_id, province_name, area_id, area_name, address_desc,
                    travel_tool, bank_name, bank_card_holder, bank_card_number, bank_card_photo,
                    emergency_contact_name, emergency_contact_relation, emergency_contact_phone,
                    emergency_contact_address, wx_open_id, wx_union_id, wx_bind_time,
                    created_by, updated_by, created_at, updated_at, start_time, end_time, rest_days
                ) VALUES (
                    :staff_id, :id_number, :birthday, :id_number_md5, :id_verify_status, :id_verify_msg,
                    :lng, :lat, :last_purpose_time, :set_servicer_top, :live_pos, :id_card_people_photo,
                    :id_card_address, :id_card_valid_from, :id_card_valid_to, :id_card_long_term,
                    :native_place, :native_place_id, :nation, :nation_id, :marriage_status,
                    :education_background, :height, :weight, :family_address, :medical_history,
                    :source, :province_id, :province_name, :area_id, :area_name, :address_desc,
                    :travel_tool, :bank_name, :bank_card_holder, :bank_card_number, :bank_card_photo,
                    :emergency_contact_name, :emergency_contact_relation, :emergency_contact_phone,
                    :emergency_contact_address, :wx_open_id, :wx_union_id, :wx_bind_time,
                    :created_by, :updated_by, NOW(), NOW(), :start_time, :end_time, :rest_days
                )
            """)

            await query_db.execute(copy_ext_sql, {
                "staff_id": new_staff_id,  # 关联到新员工
                "id_number": source_ext.id_number,
                "birthday": source_ext.birthday,
                "id_number_md5": source_ext.id_number_md5,
                "id_verify_status": source_ext.id_verify_status,
                "id_verify_msg": source_ext.id_verify_msg,
                "lng": source_ext.lng,
                "lat": source_ext.lat,
                "last_purpose_time": source_ext.last_purpose_time,
                "set_servicer_top": source_ext.set_servicer_top,
                "live_pos": source_ext.live_pos,
                "id_card_people_photo": source_ext.id_card_people_photo,
                "id_card_address": source_ext.id_card_address,
                "id_card_valid_from": source_ext.id_card_valid_from,
                "id_card_valid_to": source_ext.id_card_valid_to,
                "id_card_long_term": source_ext.id_card_long_term,
                "native_place": source_ext.native_place,
                "native_place_id": source_ext.native_place_id,
                "nation": source_ext.nation,
                "nation_id": source_ext.nation_id,
                "marriage_status": source_ext.marriage_status,
                "education_background": source_ext.education_background,
                "height": source_ext.height,
                "weight": source_ext.weight,
                "family_address": source_ext.family_address,
                "medical_history": source_ext.medical_history,
                "source": source_ext.source,
                "province_id": source_ext.province_id,
                "province_name": source_ext.province_name,
                "area_id": source_ext.area_id,
                "area_name": source_ext.area_name,
                "address_desc": source_ext.address_desc,
                "travel_tool": source_ext.travel_tool,
                "bank_name": source_ext.bank_name,
                "bank_card_holder": source_ext.bank_card_holder,
                "bank_card_number": source_ext.bank_card_number,
                "bank_card_photo": source_ext.bank_card_photo,
                "emergency_contact_name": source_ext.emergency_contact_name,
                "emergency_contact_relation": source_ext.emergency_contact_relation,
                "emergency_contact_phone": source_ext.emergency_contact_phone,
                "emergency_contact_address": source_ext.emergency_contact_address,
                "wx_open_id": source_ext.wx_open_id,
                "wx_union_id": source_ext.wx_union_id,
                "wx_bind_time": source_ext.wx_bind_time,
                "created_by": source_ext.created_by,
                "updated_by": source_ext.updated_by,
                "start_time": source_ext.start_time,
                "end_time": source_ext.end_time,
                "rest_days": source_ext.rest_days
            })

            logger.info(f"员工扩展信息复制成功: {source_staff_id} -> {new_staff_id}")

        except Exception as e:
            logger.error(f"复制员工扩展信息异常: {str(e)}")
            raise QueryException(message="员工扩展信息复制失败")

    @classmethod
    async def _copy_order_related_products(cls, query_db: AsyncSession, order_number: str, new_staff_id: int, target_store_uuid: str):
        """
        复制订单相关的服务产品关联

        :param query_db: 数据库会话
        :param order_number: 订单号
        :param new_staff_id: 新员工ID
        :param target_store_uuid: 目标门店UUID
        """
        try:
            logger.info(f"开始复制订单相关服务产品: 订单 {order_number} -> 员工 {new_staff_id}")

            # 查询订单的服务产品ID
            order_product_sql = text("""
                SELECT product_id
                FROM `order`
                WHERE order_number = :order_number
                LIMIT 1
            """)
            order_result = await query_db.execute(order_product_sql, {"order_number": order_number})
            order_info = order_result.fetchone()

            if not order_info or not order_info.product_id:
                logger.info(f"订单 {order_number} 没有关联产品，跳过复制")
                return

            # 为新员工创建服务产品关联
            copy_product_sql = text("""
                INSERT INTO service_product (
                    staff_id, productid, store_uuid, create_time, update_time
                ) VALUES (
                    :staff_id, :productid, :store_uuid, NOW(), NOW()
                )
            """)

            await query_db.execute(copy_product_sql, {
                "staff_id": new_staff_id,
                "productid": order_info.product_id,
                "store_uuid": target_store_uuid
            })

            logger.info(f"订单服务产品关联创建成功: 订单 {order_number} 产品 {order_info.product_id} -> 员工 {new_staff_id}")

        except Exception as e:
            logger.error(f"复制订单服务产品异常: {str(e)}")
            raise QueryException(message="订单服务产品关联失败")



    @classmethod
    async def accept_order_by_share_service(cls, query_db: AsyncSession, order_number: str, mobile: str, openid: str) -> Dict[str, Any]:
        """
        通过分享接单服务

        :param query_db: 数据库会话
        :param order_number: 订单编号
        :param mobile: 员工手机号
        :param openid: 微信openid
        :return: 接单结果
        """
        try:
            logger.info(f"开始处理分享接单: 订单号={order_number}, 手机号={mobile}")

            # 1. 验证订单状态并获取预设佣金
            order_sql = text("""
                SELECT
                    order_number,
                    store_id,
                    store_uuid,
                    store_name,
                    order_status,
                    product_name,
                    preset_commission
                FROM `order`
                WHERE order_number = :order_number
            """)

            order_result = await query_db.execute(order_sql, {"order_number": order_number})
            order_info = order_result.fetchone()

            if not order_info:
                raise ResourceNotFoundException(message=f"订单不存在: {order_number}")

            order_dict = dict(order_info._mapping)

            # 检查订单状态是否允许接单（只有已接单状态的订单支持分享接单）
            if order_dict['order_status'] != 10:
                raise BusinessException(message=f"订单状态不允许分享接单，当前状态: {order_dict['order_status']}")

            # 2. 使用行锁检查订单是否已被接单，防止竞态条件
            waiter_check_sql = text("""
                SELECT COUNT(*) as count
                FROM order_waiter
                WHERE order_number = :order_number
                FOR UPDATE
            """)

            waiter_result = await query_db.execute(waiter_check_sql, {"order_number": order_number})
            waiter_row = waiter_result.fetchone()
            waiter_count = waiter_row[0] if waiter_row else 0

            if waiter_count > 0:
                raise BusinessException(message="该订单已被其他人员接单")

            # 3. 再次检查订单状态，确保在锁定期间状态未被其他事务修改
            order_status_check_sql = text("""
                SELECT order_status
                FROM `order`
                WHERE order_number = :order_number
                FOR UPDATE
            """)

            status_result = await query_db.execute(order_status_check_sql, {"order_number": order_number})
            current_status_row = status_result.fetchone()

            if not current_status_row or current_status_row[0] != 10:
                raise BusinessException(message="订单状态已变更，无法接单")

            # 3. 查询现有员工信息（获取所有字段用于完整复制）
            staff_sql = text("""
                SELECT *
                FROM service_staff
                WHERE mobile = :mobile
                AND status = 1
                LIMIT 1
            """)

            # 4. 优先检查员工是否在订单门店存在
            target_staff_sql = text("""
                SELECT id, uuid FROM service_staff
                WHERE mobile = :mobile AND store_id = :store_id AND status = 1
                LIMIT 1
            """)

            target_result = await query_db.execute(target_staff_sql, {
                "mobile": mobile,
                "store_id": order_dict['store_id']
            })
            target_staff = target_result.fetchone()

            if target_staff:
                # 情况1：员工在目标门店已存在，直接使用
                target_staff_id = target_staff[0]  # 使用数字ID
                target_staff_uuid = target_staff[1]  # UUID用于其他查询
                logger.info(f"员工在目标门店已存在，直接使用: {mobile} -> 门店{order_dict['store_id']}, ID: {target_staff_id}")

                # 获取员工详细信息用于后续使用
                staff_detail_sql = text("""
                    SELECT *
                    FROM service_staff
                    WHERE uuid = :uuid AND status = 1
                    LIMIT 1
                """)

                staff_detail_result = await query_db.execute(staff_detail_sql, {"uuid": target_staff_uuid})
                existing_staff = staff_detail_result.fetchone()

                if not existing_staff:
                    raise BusinessException(message="员工信息获取失败")

                existing_staff_dict = dict(existing_staff._mapping)
            else:
                # 情况2：员工在目标门店不存在，查找其他门店的员工信息
                staff_result = await query_db.execute(staff_sql, {"mobile": mobile})
                existing_staff = staff_result.fetchone()

                if not existing_staff:
                    # 情况3：员工在任何门店都不存在，需要跳转到员工入驻申请页面
                    raise ResourceNotFoundException(message="员工信息不存在，请先注册")

                existing_staff_dict = dict(existing_staff._mapping)
                logger.info(f"在其他门店找到员工信息: {mobile} -> 原门店{existing_staff_dict['store_id']}, 需复制到目标门店{order_dict['store_id']}")

                # 情况4：员工在其他门店存在，需要完整复制到目标门店
                # 获取目标门店的正确company_id（从store表获取）
                store_company_sql = text("""
                    SELECT company_id FROM store
                    WHERE id = :store_id
                    LIMIT 1
                """)

                store_result = await query_db.execute(store_company_sql, {"store_id": order_dict['store_id']})
                store_row = store_result.fetchone()

                if not store_row:
                    raise BusinessException(message=f"无法获取门店{order_dict['store_id']}的公司信息")

                target_company_id = store_row.company_id

                # 生成新的员工UUID
                import uuid
                new_staff_uuid = str(uuid.uuid4())

                # 复制员工信息到目标门店，保留所有原有字段
                copy_staff_sql = text("""
                        INSERT INTO service_staff (
                            uuid, user_id, job_type, user_name, nick_name, real_name, mobile, password,
                            wx_openid, wx_unionid, wx_bind_time, wx_official_openid, wx_official_unionid,
                            wx_official_nickname, wx_official_avatar, wx_official_bind_time, is_default_company,
                            last_login_time, star_level, status, work_type, is_delete, is_part_time_job,
                            is_old, is_allow_rob, is_bind_wx, sex, age, avatar, city_id, city,
                            address, service_cnt, service_uv, service_commission, sale_commission,
                            own_user_id, own_user_name, insurance_start_time, insurance_end_time,
                            store_id, store_uuid, store_name, company_id, created_by, create_time, update_time
                        ) VALUES (
                            :uuid, :user_id, :job_type, :user_name, :nick_name, :real_name, :mobile, :password,
                            :wx_openid, :wx_unionid, :wx_bind_time, :wx_official_openid, :wx_official_unionid,
                            :wx_official_nickname, :wx_official_avatar, :wx_official_bind_time, :is_default_company,
                            :last_login_time, :star_level, :status, :work_type, :is_delete, :is_part_time_job,
                            :is_old, :is_allow_rob, :is_bind_wx, :sex, :age, :avatar, :city_id, :city,
                            :address, :service_cnt, :service_uv, :service_commission, :sale_commission,
                            :own_user_id, :own_user_name, :insurance_start_time, :insurance_end_time,
                            :store_id, :store_uuid, :store_name, :company_id, 'system', NOW(), NOW()
                        )
                    """)

                copy_params = {
                        "uuid": new_staff_uuid,
                        "user_id": existing_staff_dict.get('user_id', '0'),
                        "job_type": existing_staff_dict.get('job_type', '1'),
                        "user_name": existing_staff_dict.get('user_name'),
                        "nick_name": existing_staff_dict.get('nick_name'),
                        "real_name": existing_staff_dict['real_name'],
                        "mobile": mobile,
                        "password": existing_staff_dict.get('password'),
                        "wx_openid": openid,
                        "wx_unionid": existing_staff_dict.get('wx_unionid'),
                        "wx_bind_time": existing_staff_dict.get('wx_bind_time'),
                        "wx_official_openid": existing_staff_dict.get('wx_official_openid'),
                        "wx_official_unionid": existing_staff_dict.get('wx_official_unionid'),
                        "wx_official_nickname": existing_staff_dict.get('wx_official_nickname'),
                        "wx_official_avatar": existing_staff_dict.get('wx_official_avatar'),
                        "wx_official_bind_time": existing_staff_dict.get('wx_official_bind_time'),
                        "is_default_company": 0,  # 新门店不是默认公司
                        "last_login_time": existing_staff_dict.get('last_login_time'),
                        "star_level": existing_staff_dict.get('star_level', '0.0'),
                        "status": 1,  # 新复制的员工状态为正常
                        "work_type": existing_staff_dict.get('work_type', '1'),
                        "is_delete": existing_staff_dict.get('is_delete', '0'),
                        "is_part_time_job": existing_staff_dict.get('is_part_time_job', '0'),
                        "is_old": existing_staff_dict.get('is_old', '0'),
                        "is_allow_rob": existing_staff_dict.get('is_allow_rob', '1'),
                        "is_bind_wx": existing_staff_dict.get('is_bind_wx', 0),
                        "sex": existing_staff_dict.get('sex'),
                        "age": existing_staff_dict.get('age', '0'),
                        "avatar": existing_staff_dict.get('avatar'),
                        "city_id": existing_staff_dict.get('city_id'),
                        "city": existing_staff_dict.get('city'),
                        "address": existing_staff_dict.get('address'),
                        "service_cnt": existing_staff_dict.get('service_cnt', '0'),
                        "service_uv": existing_staff_dict.get('service_uv', '0'),
                        "service_commission": existing_staff_dict.get('service_commission', '0'),
                        "sale_commission": existing_staff_dict.get('sale_commission', '0'),
                        "own_user_id": existing_staff_dict.get('own_user_id', '0'),
                        "own_user_name": existing_staff_dict.get('own_user_name'),
                        "insurance_start_time": existing_staff_dict.get('insurance_start_time'),
                        "insurance_end_time": existing_staff_dict.get('insurance_end_time'),
                        # 新门店相关字段
                        "store_id": order_dict['store_id'],
                        "store_uuid": order_dict['store_uuid'],
                        "store_name": order_dict['store_name'],
                        "company_id": target_company_id
                    }

                await query_db.execute(copy_staff_sql, copy_params)
                target_staff_uuid = new_staff_uuid

                # 获取新插入的员工ID
                new_staff_result = await query_db.execute(text("SELECT LAST_INSERT_ID() as new_id"))
                new_staff_row = new_staff_result.fetchone()
                target_staff_id = new_staff_row.new_id  # 使用数字ID作为service_id

                # 复制员工扩展信息
                await cls._copy_staff_ext_info(query_db, existing_staff_dict['id'], target_staff_id)

                # 复制订单相关的服务产品关联
                await cls._copy_order_related_products(query_db, order_number, target_staff_id, order_dict['store_uuid'])

                logger.info(f"完整复制员工信息到目标门店: {mobile} -> 门店{order_dict['store_id']}, 新UUID: {new_staff_uuid}, 新ID: {target_staff_id} (包含扩展信息、订单服务产品)")

            # 5. 使用员工手机号检查是否已经存在订单员工关联记录，防止重复插入
            check_waiter_sql = text("""
                SELECT COUNT(*) FROM order_waiter ow
                INNER JOIN service_staff ss ON ow.service_id = CAST(ss.id AS CHAR)
                WHERE ow.order_number = :order_number AND ss.mobile = :mobile
                FOR UPDATE
            """)

            check_result = await query_db.execute(check_waiter_sql, {
                "order_number": order_number,
                "mobile": mobile
            })
            existing_count = check_result.scalar() or 0

            if existing_count > 0:
                raise BusinessException(message="该员工已经接过此订单，无法重复接单")

            # 6. 创建订单员工关联记录，使用预设佣金
            preset_commission = order_dict.get('preset_commission', 0)

            waiter_sql = text("""
                INSERT INTO order_waiter (
                    order_number, service_id, service_name, service_personal, service_personal_commission, create_time
                ) VALUES (
                    :order_number, :service_id, :service_name, :service_personal, :service_personal_commission, NOW()
                )
            """)

            # 如果有预设佣金，使用预设佣金金额；否则使用手机号（保持原有逻辑）
            if preset_commission > 0:
                service_personal_value = str(preset_commission)
                service_personal_commission_value = 0  # 预设佣金模式，commission字段为0
                logger.info(f"使用预设佣金模式: 佣金金额={preset_commission}")
            else:
                service_personal_value = mobile
                service_personal_commission_value = 0  # 默认为0
                logger.info(f"使用默认模式: 手机号={mobile}")

            waiter_params = {
                "order_number": order_number,
                "service_id": target_staff_id,  # 修复：使用员工的数字ID而不是UUID
                "service_name": existing_staff_dict['real_name'],
                "service_personal": service_personal_value,
                "service_personal_commission": service_personal_commission_value
            }

            logger.info(f"创建订单员工关联记录: 订单号={order_number}, 员工={existing_staff_dict['real_name']}, service_id={target_staff_id}, service_personal={service_personal_value}, service_personal_commission={service_personal_commission_value}")

            await query_db.execute(waiter_sql, waiter_params)

            # 6. 更新订单状态为已派单，并清空预设佣金
            update_order_sql = text("""
                UPDATE `order`
                SET order_status = 40, order_status_name = '已派单',
                    preset_commission = 0, update_time = NOW()
                WHERE order_number = :order_number
            """)

            await query_db.execute(update_order_sql, {"order_number": order_number})

            # 提交事务
            await query_db.commit()

            logger.info(f"分享接单成功: 订单号={order_number}, 员工={existing_staff_dict['real_name']}")

            return {
                "order_number": order_number,
                "staff_name": existing_staff_dict['real_name'],
                "staff_mobile": mobile,
                "message": "接单成功"
            }

        except (ResourceNotFoundException, ValidationException, BusinessException) as e:
            await query_db.rollback()
            logger.error(f"分享接单业务异常: {e.message}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"分享接单失败: {str(e)}")
            raise BusinessException(message=f"分享接单失败: {str(e)}")
