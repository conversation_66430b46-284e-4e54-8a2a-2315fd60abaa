<template>
  <view class="order-detail-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="detail-header">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="nav-left" @click="goBack">
            <view class="back-btn">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
          </view>
          <view class="nav-center">
            <text class="nav-title">订单详情</text>
          </view>
          <view class="nav-right">
            <!-- 移除分享按钮 -->
          </view>
        </view>

        <!-- 订单状态卡片 -->
        <view class="order-status-card">
          <view class="status-main">
            <view class="status-info">
              <text class="status-title">{{ orderDetail.order_info.order_status_name }}</text>
              <text class="order-number">#{{ orderDetail.order_info.order_number }}</text>
            </view>
            <view class="status-amount" v-if="shouldShowAmount">
              <text class="amount-label">订单金额</text>
              <text class="amount-value">¥{{ orderDetail.order_info.total_amount || '0' }}</text>
            </view>
          </view>
          <view class="status-time" v-if="orderDetail.order_info.service_time">
            <view class="time-icon">
              <u-icon name="clock" size="14" color="rgba(255,255,255,0.8)"></u-icon>
            </view>
            <text>服务时间：{{ orderDetail.order_info.service_time }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 订单详情内容 -->
    <view v-else-if="orderDetail" class="detail-content">

      <!-- 客户信息卡片 -->
      <view class="info-card customer-card">
        <view class="card-header">
          <view class="header-icon customer-icon">
            <u-icon name="account" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">客户信息</text>
        </view>
        <view class="customer-content">
          <view class="customer-avatar">
            <image
              v-if="orderDetail.customer_info.customer_avatar"
              :src="orderDetail.customer_info.customer_avatar"
              class="avatar-img"
              mode="aspectFill"
            />
            <view v-else class="avatar-placeholder">
              <u-icon name="account" size="24" color="#999"></u-icon>
            </view>
          </view>
          <view class="customer-info">
            <view class="customer-name">{{ orderDetail.customer_info.customer_name || '未知客户' }}</view>
            <view class="customer-phone">{{ orderDetail.customer_info.customer_mobile || '无联系方式' }}</view>
          </view>
          <view class="contact-actions">
            <view class="action-btn call-btn" @click="contactCustomer(orderDetail.customer_info.customer_mobile)">
              <u-icon name="phone" size="16" color="#fff"></u-icon>
              <text>拨打</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分享佣金设置卡片 - 仅在已接单状态下显示 -->
      <view class="info-card commission-card" v-if="orderDetail.order_info.order_status === 10">
        <view class="card-header">
          <view class="header-icon commission-icon">
            <u-icon name="gift" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">分享订单佣金</text>
        </view>
        <view class="commission-content">
          <view class="commission-info">
            <view class="commission-row">
              <text class="commission-label">当前佣金：</text>
              <text class="commission-value">¥{{ orderDetail.order_info.preset_commission || '0.00' }}</text>
            </view>
          </view>
          <view class="commission-actions">
            <view class="action-btn edit-commission-btn" @click="openCommissionModal">
              <u-icon name="edit-pen" size="16" color="#fff"></u-icon>
              <text>设置佣金</text>
            </view>
            <view class="action-btn share-btn" @click="shareOrder" v-if="orderDetail.order_info.preset_commission > 0">
              <u-icon name="share" size="16" color="#fff"></u-icon>
              <text>分享订单</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务地址卡片 -->
      <view class="info-card address-card">
        <view class="card-header">
          <view class="header-icon address-icon">
            <u-icon name="map" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务地址</text>
        </view>
        <view class="address-content">
          <view class="address-main">
            <text class="address-text">{{ orderDetail.address_info.service_address || '地址未设置' }}</text>
          </view>
          <view class="address-desc" v-if="orderDetail.address_info.address_desc">
            <text class="desc-label">详细描述：</text>
            <text class="desc-text">{{ orderDetail.address_info.address_desc }}</text>
          </view>
          <view class="address-actions" v-if="orderDetail.address_info.lng && orderDetail.address_info.lat">
            <view class="action-btn map-btn">
              <u-icon name="map" size="16" color="#fff"></u-icon>
              <text>导航</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务项目卡片 -->
      <view class="info-card service-card">
        <view class="card-header">
          <view class="header-icon service-icon">
            <u-icon name="list" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务项目</text>
        </view>
        <view class="service-content">
          <view class="service-item">
            <view class="service-name">{{ orderDetail.product_info.product_name || '未知项目' }}</view>
            <view class="service-details">
              <text class="detail-tag">{{ orderDetail.product_info.service_type_name }}</text>
              <text class="detail-tag">{{ orderDetail.product_info.product_type_name }}</text>
              <text class="detail-tag">{{ orderDetail.product_info.buy_num }}{{ orderDetail.product_info.product_unit_name || '次' }}</text>
            </view>
          </view>
          <view class="service-source" v-if="orderDetail.raw_data && orderDetail.raw_data.source">
            <text class="source-label">订单来源：</text>
            <text class="source-value">{{ orderSourceName }}</text>
          </view>
        </view>
      </view>

      <!-- 服务人员卡片 -->
      <view class="info-card staff-card" v-if="orderDetail.staff_info && orderDetail.staff_info.length > 0">
        <view class="card-header">
          <view class="header-icon staff-icon">
            <u-icon name="man" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务人员</text>
        </view>
        <view class="staff-content">
          <view class="staff-item" v-for="(staff, index) in orderDetail.staff_info" :key="index">
            <view class="staff-avatar">
              <view class="avatar-placeholder">
                <u-icon name="account" size="20" color="#999"></u-icon>
              </view>
            </view>
            <view class="staff-info">
              <view class="staff-name">{{ staff.real_name || staff.user_name || '未知员工' }}</view>
              <view class="staff-phone" v-if="staff.mobile">{{ staff.mobile }}</view>
            </view>
            <view class="staff-commission" v-if="staff.service_personal">
              <text class="commission-label">佣金</text>
              <text class="commission-value">¥{{ staff.service_personal }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 支付信息卡片 -->
      <view class="info-card payment-card">
        <view class="card-header">
          <view class="header-icon payment-icon">
            <u-icon name="rmb-circle" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">支付信息</text>
          <!-- 待付款状态显示立即扣费按钮 -->
          <view v-if="orderDetail.payment_info.pay_status === 0 || orderDetail.payment_info.pay_status === null" class="header-action">
            <text class="pay-now-btn" @click="handleManualPayment">立即扣费</text>
          </view>
        </view>
        <view class="payment-content">
          <view class="payment-row">
            <text class="payment-label">订单金额</text>
            <text class="payment-value main-amount">¥{{ orderDetail.order_info.total_amount || '0' }}</text>
          </view>
          <view class="payment-row" v-if="orderDetail.payment_info.pay_actual">
            <text class="payment-label">实付金额</text>
            <text class="payment-value">¥{{ orderDetail.payment_info.pay_actual }}</text>
          </view>
          <view class="payment-row" v-if="orderDetail.payment_info.transaction_id">
            <text class="payment-label">支付凭证号</text>
            <text class="payment-value">{{ orderDetail.payment_info.transaction_id }}</text>
            <text class="copy-btn" @click="copyTransactionId(orderDetail.payment_info.transaction_id)">复制</text>
          </view>
          <view class="payment-row">
            <text class="payment-label">支付状态</text>
            <text class="payment-value" :class="{ 'status-success': orderDetail.payment_info.pay_status === 1, 'status-pending': orderDetail.payment_info.pay_status === 0 || orderDetail.payment_info.pay_status === null }">
              {{ orderDetail.payment_info.pay_status_name }}
            </text>
          </view>
          <view class="payment-row" v-if="orderDetail.payment_info.pay_type_name && orderDetail.payment_info.pay_type_name !== '其他'">
            <text class="payment-label">支付方式</text>
            <text class="payment-value">{{ orderDetail.payment_info.pay_type_name }}</text>
          </view>
          <view class="payment-row" v-if="orderDetail.payment_info.pay_time">
            <text class="payment-label">支付时间</text>
            <text class="payment-value">{{ formatDateTime(orderDetail.payment_info.pay_time) }}</text>
          </view>
        </view>
      </view>

      <!-- 订单详情卡片 -->
      <view class="info-card order-card">
        <view class="card-header">
          <view class="header-icon order-icon">
            <u-icon name="file-text" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">订单详情</text>
        </view>
        <view class="order-content">
          <view class="order-row">
            <text class="order-label">订单编号</text>
            <view class="order-value-with-action">
              <text class="order-value">{{ orderDetail.order_info.order_number }}</text>
              <text class="copy-btn" @click="copyOrderId(orderDetail.order_info.order_number)">复制</text>
            </view>
          </view>
          <view class="order-row">
            <text class="order-label">创建时间</text>
            <text class="order-value">{{ formatDateTime(orderDetail.order_info.create_time) }}</text>
          </view>
          <view class="order-row">
            <text class="order-label">更新时间</text>
            <text class="order-value">{{ formatDateTime(orderDetail.order_info.update_time) }}</text>
          </view>
          <view class="order-row">
            <text class="order-label">归属门店</text>
            <text class="order-value">{{ orderDetail.order_info.store_name || '未知门店' }}</text>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="info-card remark-card" v-if="hasRemarks">
        <view class="card-header">
          <view class="header-icon remark-icon">
            <u-icon name="chat" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">备注信息</text>
        </view>
        <view class="remark-content">
          <view class="remark-item" v-if="orderDetail.remark_info.remark">
            <text class="remark-label">客户留言</text>
            <text class="remark-text">{{ orderDetail.remark_info.remark }}</text>
          </view>
          <view class="remark-item" v-if="orderDetail.remark_info.service_remark">
            <text class="remark-label">服务备注</text>
            <text class="remark-text">{{ orderDetail.remark_info.service_remark }}</text>
          </view>
          <view class="remark-item" v-if="orderDetail.remark_info.after_sale_remark">
            <text class="remark-label">售后备注</text>
            <text class="remark-text">{{ orderDetail.remark_info.after_sale_remark }}</text>
          </view>
        </view>
      </view>

    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <u-icon name="file-text" size="60" color="#ccc"></u-icon>
      <text class="empty-text">订单详情加载失败</text>
      <view class="retry-btn" @click="loadOrderDetail">
        <text>重新加载</text>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="orderDetail && orderDetail.tips_info">
      <view class="action-buttons">
        <view
          class="action-btn-bottom"
          v-for="(tip, key) in availableActions"
          :key="key"
          @click="handleAction(key, tip)"
        >
          <text>{{ tip.title }}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式选择弹窗 -->
    <PaymentMethodModal
      :visible="showPaymentModal"
      :orderInfo="orderDetail"
      @close="handlePaymentModalClose"
      @select="handlePaymentMethodSelect"
    />

    <!-- 佣金设置弹窗 -->
    <u-popup
      :show="showCommissionModal"
      @close="closeCommissionModal"
      mode="center"
      :customStyle="{ width: '85%', maxWidth: '600rpx' }"
      round="16"
    >
      <view class="commission-popup">
        <view class="popup-header">
          <text class="popup-title">设置分享订单佣金</text>
          <u-icon name="close" size="20" color="#999" @click="closeCommissionModal"></u-icon>
        </view>
        <view class="popup-content">
          <view class="order-info">
            <text class="order-number">订单号：{{ (orderDetail && orderDetail.order_info && orderDetail.order_info.order_number) || 'N/A' }}</text>
            <text class="current-amount">服务项目：{{ (orderDetail && orderDetail.product_info && orderDetail.product_info.product_name) || 'N/A' }}</text>
            <text class="current-time">订单金额：¥{{ (orderDetail && orderDetail.order_info && orderDetail.order_info.total_amount) || '0.00' }}</text>
          </view>

          <view class="input-section">
            <text class="input-label">佣金金额</text>
            <view class="input-container">
              <text class="currency-symbol">¥</text>
              <input
                type="digit"
                v-model="commissionAmount"
                placeholder="0.00"
                class="amount-input"
                @input="validateCommissionInput"
              />
            </view>
            <text class="input-tip">佣金金额不能超过订单总金额</text>
          </view>
        </view>
        <view class="popup-buttons">
          <view class="popup-cancel" @click="closeCommissionModal">取消</view>
          <view class="popup-confirm" @click="confirmCommission">确认设置</view>
        </view>
      </view>
    </u-popup>

  </view>
</template>

<script>
import { get } from '@/utlis/require.js'
import { mapState } from 'vuex'
import { setPresetCommission } from '@/api/order.js'
import PaymentMethodModal from '@/components/PaymentMethodModal.vue'

export default {
  name: 'OrderDetailNew',
  components: {
    PaymentMethodModal
  },

  data() {
    return {
      loading: false,
      orderDetail: null,
      orderNumber: '',

      // 支付弹窗相关
      showPaymentModal: false,

      // 佣金设置相关
      showCommissionModal: false,
      commissionAmount: '',
      shareData: null
    };
  },

  computed: {
    ...mapState(['StatusBar']),

    statusClass() {
      if (!this.orderDetail || !this.orderDetail.order_info) {
        return 'status-default';
      }

      const status = String(this.orderDetail.order_info.order_status);

      // 已完成、已评价
      if (status === '80' || status === '90' || status === '4') {
        return 'status-completed';
      }
      // 待支付、已接单
      else if (status === '0' || status === '10') {
        return 'status-waiting';
      }
      // 服务中相关状态
      else if (status === '50' || status === '60' || status === '70' || status === '6' || status === '7') {
        return 'status-serving';
      }
      // 派单相关状态
      else if (status === '20' || status === '40' || status === '2' || status === '3') {
        return 'status-dispatching';
      }
      // 已取消、拒绝接单
      else if (status === '99' || status === '30' || status === '5') {
        return 'status-cancelled';
      }
      // 已支付
      else if (status === '1') {
        return 'status-paid';
      }

      return 'status-default';
    },

    shouldShowAmount() {
      if (!this.orderDetail || !this.orderDetail.order_info) {
        return false;
      }

      const status = String(this.orderDetail.order_info.order_status);
      return status === '0' || status === '10';
    },

    orderSourceName() {
      if (!this.orderDetail || !this.orderDetail.raw_data || !this.orderDetail.raw_data.source) {
        return '未知';
      }

      const source = this.orderDetail.raw_data.source;
      const sourceMap = {
        'proxy': '代客下单',
        'client': '门店端',
        'admin': '管理后台',
        'staff': '服务人员',
        'wechat': '微信小程序',
        'app': '手机应用'
      };
      return sourceMap[source] || source || '未知';
    },

    hasRemarks() {
      if (!this.orderDetail || !this.orderDetail.remark_info) {
        return false;
      }

      const remarks = this.orderDetail.remark_info;
      return !!(remarks.remark || remarks.service_remark || remarks.after_sale_remark);
    },

    availableActions() {
      if (!this.orderDetail || !this.orderDetail.tips_info) {
        return {};
      }

      // 过滤出可执行的操作
      const actions = {};
      Object.keys(this.orderDetail.tips_info).forEach(key => {
        const tip = this.orderDetail.tips_info[key];
        if (tip.remark === '可执行') {
          actions[key] = tip;
        }
      });

      return actions;
    }
  },

  onLoad(options) {
    if (options.orderNumber) {
      this.orderNumber = options.orderNumber;
      this.loadOrderDetail();
    } else {
      uni.showToast({
        title: '订单号不能为空',
        icon: 'error'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },

  onShow() {
    // 页面显示时重新加载数据，确保显示最新的佣金信息
    console.log('页面显示，重新加载订单详情');
    if (this.orderNumber) {
      this.loadOrderDetail();
    }
  },

  methods: {
    async loadOrderDetail() {
      if (!this.orderNumber) {
        uni.showToast({
          title: '订单号不能为空',
          icon: 'error'
        });
        return;
      }

      this.loading = true;
      try {
        const response = await get('/api/v1/order/findServeOrderDetail', {
          order_number: this.orderNumber
        });

        // get方法已经处理了业务逻辑，直接使用返回的数据
        this.orderDetail = response;
        console.log('订单详情:', this.orderDetail);
        console.log('当前佣金:', this.orderDetail?.order_info?.preset_commission);
      } catch (error) {
        console.error('获取订单详情失败:', error);
        uni.showToast({
          title: '获取订单详情失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    goBack() {
      uni.navigateBack();
    },

    copyTransactionId(transactionId) {
      if (!transactionId) {
        uni.showToast({
          title: '支付凭证号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: transactionId,
        success: () => {
          uni.showToast({
            title: '支付凭证号已复制',
            icon: 'success',
          });
        },
      });
    },

    async handleManualPayment() {
      if (!this.orderDetail || !this.orderDetail.order_info.order_number) {
        uni.showToast({
          title: '订单信息异常',
          icon: 'none'
        });
        return;
      }

      // 显示支付方式选择弹窗
      this.showPaymentModal = true;
    },

    // 支付弹窗关闭
    handlePaymentModalClose() {
      this.showPaymentModal = false;
    },

    // 支付方式选择
    handlePaymentMethodSelect(method) {
      this.showPaymentModal = false;

      if (method.key === 'store_deduct') {
        // 门店代扣：执行原有的扣费逻辑
        this.performStoreDeductPayment();
      }
    },

    // 执行门店代扣支付
    async performStoreDeductPayment() {
      // 获取最新的订单金额
      const currentAmount = this.orderDetail.order_info.total_amount ||
                           this.orderDetail.payment_info.pay_actual ||
                           this.orderDetail.order_info.pay_actual || '0';

      // 先显示确认对话框，显示具体扣费金额
      uni.showModal({
        title: '确认门店代扣',
        content: `确定要对订单 ${this.orderDetail.order_info.order_number} 进行门店代扣吗？\n\n扣费金额：¥${currentAmount}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performManualPayment();
          }
        }
      });
    },

    async performManualPayment() {
      try {
        uni.showLoading({
          title: '正在扣费...',
          mask: true
        });

        const response = await this.$post('/api/v1/order/manual-balance-payment', {
          order_number: this.orderDetail.order_info.order_number
        }, {
          contentType: 'application/json'
        });

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '扣费成功',
            icon: 'success'
          });

          // 重新加载订单详情
          setTimeout(() => {
            this.loadOrderDetail();
          }, 1500);
        }
      } catch (error) {
        console.error('手动扣费失败:', error);
        uni.hideLoading();

        let errorMessage = '扣费失败，请重试';
        if (error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';

      try {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return dateTime;
      }
    },

    contactCustomer(mobile) {
      if (!mobile) {
        uni.showToast({
          title: '客户手机号为空',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          // 如果拨打失败，复制到剪贴板
          uni.setClipboardData({
            data: mobile,
            success: () => {
              uni.showToast({
                title: '手机号已复制',
                icon: 'success'
              });
            }
          });
        }
      });
    },

    copyOrderId(orderId) {
      uni.setClipboardData({
        data: orderId,
        success: () => {
          uni.showToast({
            title: '订单号已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'error'
          });
        }
      });
    },

    handleAction(actionKey, actionInfo) {
      console.log('执行操作:', actionKey, actionInfo);

      // 根据不同的操作类型执行不同的逻辑
      switch (actionKey) {
        case 'cancel_order':
          this.cancelOrder();
          break;
        case 'finish_order':
          this.finishOrder();
          break;
        case 'cancel_serve':
          this.cancelServe();
          break;
        case 'finish_serve':
          this.finishServe();
          break;
        case 'edit_serve_num':
          this.editServeNum();
          break;
        case 'edit_serve_time':
          this.editServeTime();
          break;
        case 'edit_serve_user':
          this.editServeUser();
          break;
        default:
          uni.showToast({
            title: `${actionInfo.title}功能开发中`,
            icon: 'none'
          });
      }
    },

    cancelOrder() {
      uni.showModal({
        title: '取消订单',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '取消订单功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    finishOrder() {
      uni.showModal({
        title: '完成订单',
        content: '确定要完成这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '完成订单功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    cancelServe() {
      uni.showModal({
        title: '取消服务单',
        content: '取消后视为服务未完成，需要重新预约服务，请确认是否取消服务',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '取消服务单功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    finishServe() {
      uni.showModal({
        title: '完成服务单',
        content: '确定要完成这个服务单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '完成服务单功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    editServeNum() {
      uni.showToast({
        title: '修改服务单数量功能开发中',
        icon: 'none'
      });
    },

    editServeTime() {
      uni.showToast({
        title: '修改服务时间功能开发中',
        icon: 'none'
      });
    },

    editServeUser() {
      uni.showToast({
        title: '修改服务人员功能开发中',
        icon: 'none'
      });
    },

    // 佣金设置相关方法
    openCommissionModal() {
      this.commissionAmount = String((this.orderDetail && this.orderDetail.order_info && this.orderDetail.order_info.preset_commission) || '');
      this.showCommissionModal = true;
    },

    closeCommissionModal() {
      this.showCommissionModal = false;
      this.commissionAmount = '';
    },

    validateCommissionInput(e) {
      let value = e.detail.value;
      // 只允许数字和一个小数点
      value = value.replace(/[^\d.]/g, '');
      // 只允许一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      // 限制小数点后两位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
      this.commissionAmount = value;
    },

    async confirmCommission() {
      if (!this.commissionAmount) {
        uni.showToast({
          title: '请输入佣金金额',
          icon: 'none'
        });
        return;
      }

      const commission = parseFloat(this.commissionAmount);
      if (isNaN(commission) || commission <= 0) {
        uni.showToast({
          title: '请输入有效的佣金金额',
          icon: 'none'
        });
        return;
      }

      // 验证佣金不能超过订单金额
      const orderAmount = parseFloat((this.orderDetail && this.orderDetail.order_info && this.orderDetail.order_info.total_amount) || 0);
      if (commission > orderAmount) {
        uni.showToast({
          title: '佣金金额不能超过订单金额',
          icon: 'none'
        });
        return;
      }

      try {
        uni.showLoading({
          title: '设置佣金中...'
        });

        // 调用API设置预设佣金
        const response = await setPresetCommission({
          order_number: this.orderDetail.order_info.order_number,
          preset_commission: commission
        });

        uni.hideLoading();

        console.log('设置预设佣金API响应:', response);

        // 根据API响应结构判断成功
        if (response) {
          // 先更新本地订单数据
          if (this.orderDetail && this.orderDetail.order_info) {
            this.orderDetail.order_info.preset_commission = commission;
            console.log('本地佣金数据已更新:', commission);
          }

          // 设置分享数据
          this.shareData = {
            preset_commission: commission,
            share_url: response.share_url || `/pages-public/order-share?orderNumber=${this.orderDetail.order_info.order_number}&commission=${commission}`,
            order_number: this.orderDetail.order_info.order_number,
            ...response
          };

          // 关闭弹窗
          this.closeCommissionModal();

          uni.showToast({
            title: '佣金设置成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '设置佣金失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('设置预设佣金失败:', error);

        // 确保弹窗关闭，即使API调用失败
        this.closeCommissionModal();

        uni.showToast({
          title: error.message || error.msg || '设置佣金失败',
          icon: 'none'
        });
      }
    },

    shareOrder() {
      const commission = (this.orderDetail && this.orderDetail.order_info && this.orderDetail.order_info.preset_commission) || 0;

      // 确保shareData存在
      if (!this.shareData) {
        this.shareData = {
          preset_commission: commission,
          share_url: `/pages-public/order-share?orderNumber=${this.orderDetail.order_info.order_number}&commission=${commission}`
        };
      }

      // 触发微信小程序原生分享
      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          uni.showToast({
            title: '请点击右上角分享',
            icon: 'none'
          });
        },
        fail: (err) => {
          console.error('分享菜单显示失败:', err);
          uni.showToast({
            title: '分享功能暂不可用',
            icon: 'none'
          });
        }
      });
    }
  },

  // 微信分享配置
  onShareAppMessage() {
    if (this.orderDetail && this.orderDetail.order_info) {
      const orderInfo = this.orderDetail.order_info;
      const commission = orderInfo.preset_commission || this.commissionAmount || '0';

      console.log('微信分享配置:', {
        orderInfo,
        commission
      });

      // 始终返回订单分享配置，无论是否设置佣金
      // 构建分享链接参数
      let shareParams = `orderNumber=${orderInfo.order_number}`

      // 添加佣金参数
      if (commission && commission > 0) {
        shareParams += `&commission=${commission}`
      }

      // 添加门店信息（用于快速入驻）
      if (orderInfo.store_uuid) {
        shareParams += `&storeUuid=${orderInfo.store_uuid}`
      }
      if (orderInfo.store_name) {
        shareParams += `&storeName=${encodeURIComponent(orderInfo.store_name)}`
      }

      // 添加分享员工信息（用作邀请码）
      const currentUser = this.$store.state.user || this.$store.state.userInfo
      if (currentUser && currentUser.id) {
        shareParams += `&inviterUserId=${currentUser.id}`
        console.log('添加分享员工ID到分享链接:', currentUser.id)
      } else {
        console.warn('未获取到当前用户信息，无法添加邀请码')
        console.log('Store状态:', {
          user: this.$store.state.user,
          userInfo: this.$store.state.userInfo
        })
      }

      return {
        title: `【急招】${(this.orderDetail.product_info && this.orderDetail.product_info.product_name) || '家政服务'}${commission && commission > 0 ? ` - 佣金¥${commission}` : ''}`,
        path: `/pages-public/order-share?${shareParams}`,
        imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
      };
    }

    // 默认分享配置
    return {
      title: '家政服务订单 - 急招服务人员',
      path: '/pages-dispatch/order-detail-new',
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
    };
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 现代化头部
.detail-header {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }

  .top-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
    height: 60rpx;

    .nav-left, .nav-right {
      width: 60rpx;
      display: flex;
      align-items: center;
    }

    .nav-center {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .back-btn {
      width: 40rpx;
      height: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }

    .share-btn {
      width: 40rpx;
      height: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }

    .nav-title {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }
  }

  .order-status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    padding: 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.3);

    .status-main {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20rpx;
    }

    .status-info {
      flex: 1;
    }

    .status-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .order-number {
      font-size: 24rpx;
      color: #666;
    }

    .status-amount {
      text-align: right;
    }

    .amount-label {
      font-size: 24rpx;
      color: #666;
      display: block;
      margin-bottom: 5rpx;
    }

    .amount-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff801b;
    }

    .status-time {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #666;

      .time-icon {
        margin-right: 8rpx;
        display: flex;
        align-items: center;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;

  .loading-text {
    margin-top: 20rpx;
    color: #999;
    font-size: 28rpx;
  }
}

.detail-content {
  padding: 0 20rpx 200rpx;
}



/* 信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .header-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15rpx;

    &.customer-icon {
      background: linear-gradient(135deg, #09be89 0%, #00a862 100%);
    }

    &.address-icon {
      background: linear-gradient(135deg, #007aff 0%, #0066cc 100%);
    }

    &.service-icon {
      background: linear-gradient(135deg, #ff801b 0%, #ff403f 100%);
    }

    &.staff-icon {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    }

    &.payment-icon {
      background: linear-gradient(135deg, #ff9500 0%, #e6850e 100%);
    }

    &.order-icon {
      background: linear-gradient(135deg, #666 0%, #333 100%);
    }

    &.remark-icon {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    }

    &.commission-icon {
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    }
  }

  .card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    flex: 1;
    margin-left: 20rpx;
  }

  .header-action {
    margin-left: auto;

    .pay-now-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      border: none;
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.3);
    }
  }
}

/* 客户信息卡片 */
.customer-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;

  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.customer-phone {
  font-size: 28rpx;
  color: #666;
}

.contact-actions {
  .action-btn {
    padding: 15rpx 25rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      margin-left: 8rpx;
      font-size: 24rpx;
    }
  }

  .call-btn {
    background-color: #00cc66;
    color: #fff;
  }
}

/* 分享佣金卡片 */
.commission-content {
  padding: 30rpx;
}

.commission-info {
  margin-bottom: 25rpx;
}

.commission-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.commission-label {
  font-size: 28rpx;
  color: #666;
}

.commission-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;

  &:first-of-type {
    color: #ff6b35;
  }
}

.commission-actions {
  display: flex;
  gap: 15rpx;

  .action-btn {
    flex: 1;
    padding: 15rpx 25rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      margin-left: 8rpx;
      font-size: 24rpx;
    }
  }

  .edit-commission-btn {
    background-color: #ff6b35;
    color: #fff;
  }

  .share-btn {
    background-color: #00cc66;
    color: #fff;
  }
}

/* 地址卡片 */
.address-content {
  padding: 30rpx;
}

.address-main {
  margin-bottom: 20rpx;
}

.address-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

.address-desc {
  margin-bottom: 20rpx;

  .desc-label {
    font-size: 26rpx;
    color: #666;
  }

  .desc-text {
    font-size: 26rpx;
    color: #333;
    margin-left: 10rpx;
  }
}

.address-actions {
  .map-btn {
    background-color: #007aff;
    color: #fff;
    padding: 15rpx 25rpx;
    border-radius: 30rpx;
    display: inline-flex;
    align-items: center;

    text {
      margin-left: 8rpx;
      font-size: 24rpx;
    }
  }
}

/* 服务项目卡片 */
.service-content {
  padding: 30rpx;
}

.service-item {
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.service-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.detail-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.service-source {
  .source-label {
    font-size: 26rpx;
    color: #666;
  }

  .source-value {
    font-size: 26rpx;
    color: #333;
    margin-left: 10rpx;
  }
}

/* 服务人员卡片 */
.staff-content {
  padding: 30rpx;
}

.staff-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.staff-avatar {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.staff-phone {
  font-size: 26rpx;
  color: #666;
}

.staff-commission {
  text-align: right;

  .commission-label {
    font-size: 24rpx;
    color: #666;
    display: block;
    margin-bottom: 5rpx;
  }

  .commission-value {
    font-size: 28rpx;
    font-weight: bold;
    color: #ff6600;
  }
}

/* 支付信息卡片 */
.payment-content {
  padding: 30rpx;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-label {
  font-size: 28rpx;
  color: #666;
}

.payment-value {
  font-size: 28rpx;
  color: #333;

  &.main-amount {
    font-size: 32rpx;
    font-weight: bold;
    color: #ff6600;
  }

  &.status-success {
    color: #00cc66;
  }

  &.status-pending {
    color: #ff9500;
  }
}

/* 订单详情卡片 */
.order-content {
  padding: 30rpx;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.order-label {
  font-size: 28rpx;
  color: #666;
}

.order-value {
  font-size: 28rpx;
  color: #333;
}

.order-value-with-action {
  display: flex;
  align-items: center;

  .copy-btn {
    margin-left: 15rpx;
    padding: 8rpx 16rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border-radius: 20rpx;
    font-size: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.3);
  }
}

/* 备注信息卡片 */
.remark-content {
  padding: 30rpx;
}

.remark-item {
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.remark-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;

  .empty-text {
    margin: 20rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .retry-btn {
    padding: 20rpx 40rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border-radius: 30rpx;
    font-size: 28rpx;
    box-shadow: 0 4rpx 20rpx rgba(253, 209, 24, 0.3);
  }
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn-bottom {
  flex: 1;
  padding: 25rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  color: #fff;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 20rpx rgba(253, 209, 24, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 10rpx rgba(253, 209, 24, 0.2);
  }
}

/* 佣金设置弹窗样式 */
.commission-popup {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .popup-content {
    padding: 30rpx;

    .order-info {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 30rpx;

      text {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .order-number {
        color: #333;
        font-weight: bold;
      }
    }

    .input-section {
      .input-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 15rpx;
        display: block;
        font-weight: bold;
      }

      .input-container {
        display: flex;
        align-items: center;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        padding: 0 20rpx;
        margin-bottom: 15rpx;

        .currency-symbol {
          font-size: 28rpx;
          color: #666;
          margin-right: 10rpx;
        }

        .amount-input {
          flex: 1;
          height: 80rpx;
          font-size: 28rpx;
          color: #333;
        }
      }

      .input-tip {
        font-size: 24rpx;
        color: #999;
        line-height: 1.4;
      }
    }
  }

  .popup-buttons {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .popup-cancel,
    .popup-confirm {
      flex: 1;
      padding: 30rpx;
      text-align: center;
      font-size: 28rpx;
      cursor: pointer;
    }

    .popup-cancel {
      color: #666;
      border-right: 1rpx solid #f0f0f0;
    }

    .popup-confirm {
      color: #ff6b35;
      font-weight: bold;
    }
  }
}
</style>
