<template>
  <view class="container">
    <!-- 顶部背景区域 -->
    <view class="header-background-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航栏 -->
        <view class="header-section">
          <view class="navbar">
            <view class="nav-back" @tap="goBack">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
            <text class="nav-title">改时改价</text>
            <view class="nav-placeholder"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 订单信息卡片 -->
      <view class="form-card">
        <view class="card-title">订单信息</view>
        <view class="form-item">
          <view class="item-label">订单编号</view>
          <view class="item-value">{{ orderDetail.order_number || '加载中...' }}</view>
        </view>
        <view class="form-item">
          <view class="item-label">客户姓名</view>
          <view class="item-value">{{ orderDetail.customer_name || '加载中...' }}</view>
        </view>
        <view class="form-item">
          <view class="item-label">服务项目</view>
          <view class="item-value">{{ orderDetail.product_name || '加载中...' }}</view>
        </view>
        <view class="form-item">
          <view class="item-label">原订单金额</view>
          <view class="item-value original-amount">¥{{ orderDetail.total_amount || '0.00' }}</view>
        </view>
      </view>

      <!-- 时间修改卡片 -->
      <view class="form-card">
        <view class="card-title">时间修改</view>
        <!-- 服务日期 -->
        <view class="form-item arrow" @tap="selectDate">
          <view class="item-label">
            <text class="required">*</text>
            <text>服务日期</text>
          </view>
          <view class="item-value">
            <text>{{ serviceDate }}</text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>

        <!-- 服务时间 -->
        <view class="form-item arrow" @tap="selectTime">
          <view class="item-label">
            <text class="required">*</text>
            <text>服务时间</text>
          </view>
          <view class="item-value">
            <text>{{ serviceTime }}</text>
            <u-icon name="arrow-right" color="#999" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 价格修改卡片 -->
      <view class="form-card">
        <view class="card-title">价格修改</view>
        <!-- 新订单金额 -->
        <view class="form-item">
          <view class="item-label">
            <text class="required">*</text>
            <text>新订单金额（元）</text>
          </view>
          <view class="item-value">
            <input
              type="number"
              v-model="newAmount"
              placeholder="请输入新金额"
              class="amount-input"
              @input="validateAmount"
            />
          </view>
        </view>

        <!-- 金额变化提示 -->
        <view class="form-item" v-if="amountChange !== 0">
          <view class="item-label">金额变化</view>
          <view class="item-value" :class="{ 'increase': amountChange > 0, 'decrease': amountChange < 0 }">
            {{ amountChange > 0 ? '+' : '' }}¥{{ Math.abs(amountChange).toFixed(2) }}
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="form-card">
        <view class="card-title">备注信息</view>
        <!-- 改价原因 -->
        <view class="note-section">
          <view class="note-title">改价原因（可选）</view>
          <view class="note-input-wrapper">
            <textarea
              v-model="priceChangeReason"
              placeholder="请输入改价原因，如：客户要求增加服务项目、材料费用调整等"
              class="note-input"
              maxlength="200"
            />
            <view class="word-count">{{ priceChangeReason.length }}/200</view>
          </view>
        </view>

        <!-- 客户备注 -->
        <view class="note-section">
          <view class="note-title">客户备注（可选）</view>
          <view class="note-input-wrapper">
            <textarea v-model="customerNote" placeholder="请输入客户备注" class="note-input" maxlength="200" />
            <view class="word-count">{{ customerNote.length }}/200</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 确认按钮 -->
    <view class="confirm-btn primary-btn" @tap="saveChanges">
      <text>确认修改</text>
    </view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      ref="datetimePicker"
      v-model="selectedDateTime"
      mode="date"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="onDateConfirm"
      @cancel="onDateCancel"
    ></u-datetime-picker>

    <!-- 时间选择器 -->
    <u-picker
      ref="timePicker"
      :show="showTimePicker"
      :columns="timeColumns"
      @confirm="onTimeConfirm"
      @cancel="onTimeCancel"
    ></u-picker>
  </view>
</template>

<script>
import { get } from '@/utlis/require.js'
import { updateOrderAmount, updateOrderTime } from '@/api/order.js'

export default {
  data() {
    return {
      orderNumber: null,
      orderDetail: {},
      loading: false,

      // 时间相关
      serviceDate: '',
      serviceTime: '',
      originalServiceDate: '',
      originalServiceTime: '',

      // 金额相关
      newAmount: '',
      originalAmount: 0,

      // 备注
      priceChangeReason: '',
      customerNote: '',

      // 选择器相关
      showTimePicker: false,
      selectedDateTime: null,
      minDate: new Date().getTime(),
      maxDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).getTime(),
      timeColumns: [
        Array.from({ length: 24 }, (_, i) => {
          const hour = i.toString().padStart(2, '0')
          return {
            text: `${hour}:00`,
            value: hour
          }
        })
      ]
    };
  },

  computed: {
    // 计算金额变化
    amountChange() {
      if (!this.newAmount || !this.originalAmount) return 0
      return parseFloat(this.newAmount) - parseFloat(this.originalAmount)
    }
  },

  onLoad(options) {
    if (options.order_number) {
      this.orderNumber = options.order_number;
      this.loadOrderDetail();
    } else {
      uni.showToast({
        title: '订单号不能为空',
        icon: 'error'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },

  methods: {
    // 加载订单详情
    async loadOrderDetail() {
      if (!this.orderNumber) return;

      this.loading = true;
      try {
        const response = await get('/api/v1/order/findServeOrderDetail', {
          order_number: this.orderNumber
        });

        this.orderDetail = response.order_info || {};

        // 初始化表单数据
        this.originalAmount = parseFloat(this.orderDetail.total_amount || 0);
        this.newAmount = this.originalAmount.toString();

        // 解析服务时间
        const serviceDateTime = this.orderDetail.service_date || '';
        if (serviceDateTime) {
          const date = new Date(serviceDateTime);
          this.serviceDate = this.formatDate(date);
          this.serviceTime = this.formatTime(date);
          this.originalServiceDate = this.serviceDate;
          this.originalServiceTime = this.serviceTime;
        }

        console.log('订单详情加载成功:', this.orderDetail);
      } catch (error) {
        console.error('获取订单详情失败:', error);
        uni.showToast({
          title: '获取订单详情失败',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const weekday = weekdays[date.getDay()];
      return `${year}-${month}-${day} ${weekday}`;
    },

    // 格式化时间
    formatTime(date) {
      const hour = date.getHours().toString().padStart(2, '0');
      return `${hour}:00`;
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 选择服务日期
    selectDate() {
      this.$refs.datetimePicker.open();
    },

    // 选择服务时间
    selectTime() {
      this.showTimePicker = true;
    },

    // 日期确认
    onDateConfirm(value) {
      const date = new Date(value);
      this.serviceDate = this.formatDate(date);
    },

    // 日期取消
    onDateCancel() {
      // 不做任何操作
    },

    // 时间确认
    onTimeConfirm(value) {
      this.serviceTime = `${value[0].value}:00`;
      this.showTimePicker = false;
    },

    // 时间取消
    onTimeCancel() {
      this.showTimePicker = false;
    },

    // 验证金额输入
    validateAmount() {
      if (this.newAmount) {
        const amount = parseFloat(this.newAmount);
        if (amount < 0) {
          this.newAmount = '0';
        } else if (amount > 99999.99) {
          this.newAmount = '99999.99';
        }
      }
    },
    // 保存修改
    async saveChanges() {
      // 表单验证
      if (!this.serviceDate) {
        uni.showToast({
          title: '请选择服务日期',
          icon: 'none',
        });
        return;
      }

      if (!this.serviceTime) {
        uni.showToast({
          title: '请选择服务时间',
          icon: 'none',
        });
        return;
      }

      if (!this.newAmount || parseFloat(this.newAmount) <= 0) {
        uni.showToast({
          title: '请输入有效的订单金额',
          icon: 'none',
        });
        return;
      }

      // 检查是否有修改
      const hasTimeChange = this.serviceDate !== this.originalServiceDate ||
                           this.serviceTime !== this.originalServiceTime;
      const hasAmountChange = parseFloat(this.newAmount) !== this.originalAmount;

      if (!hasTimeChange && !hasAmountChange) {
        uni.showToast({
          title: '没有任何修改',
          icon: 'none',
        });
        return;
      }

      // 改价原因为可选，不再强制验证

      // 显示确认对话框
      const changeText = [];
      if (hasTimeChange) {
        changeText.push(`服务时间：${this.serviceDate} ${this.serviceTime}`);
      }
      if (hasAmountChange) {
        const change = this.amountChange > 0 ? `+${this.amountChange.toFixed(2)}` : this.amountChange.toFixed(2);
        changeText.push(`订单金额：¥${this.newAmount}（${change}）`);
      }

      uni.showModal({
        title: '确认修改',
        content: `确定要修改以下内容吗？\n\n${changeText.join('\n')}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performSave(hasTimeChange, hasAmountChange);
          }
        }
      });
    },

    // 执行保存操作
    async performSave(hasTimeChange, hasAmountChange) {
      try {
        uni.showLoading({
          title: '保存中...',
          mask: true
        });

        const promises = [];

        // 修改时间
        if (hasTimeChange) {
          const newServiceDateTime = `${this.serviceDate.split(' ')[0]} ${this.serviceTime}:00`;
          promises.push(
            updateOrderTime({
              order_number: this.orderNumber,
              new_service_date: newServiceDateTime
            })
          );
        }

        // 修改金额
        if (hasAmountChange) {
          promises.push(
            updateOrderAmount({
              order_number: this.orderNumber,
              new_amount: parseFloat(this.newAmount)
            })
          );
        }

        // 并行执行所有修改
        await Promise.all(promises);

        uni.hideLoading();

        // 显示成功提示
        let successMsg = '修改成功';
        if (hasTimeChange && hasAmountChange) {
          successMsg = '时间和金额修改成功';
        } else if (hasTimeChange) {
          successMsg = '服务时间修改成功';
        } else if (hasAmountChange) {
          successMsg = '订单金额修改成功';
        }

        uni.showToast({
          title: successMsg,
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);

      } catch (error) {
        uni.hideLoading();
        console.error('保存修改失败:', error);

        let errorMessage = '保存失败，请重试';
        if (error && error.msg) {
          errorMessage = error.msg;
        } else if (error && error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
}

// 顶部背景区域
.header-background-section {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }
}

// 顶部导航区域
.header-section {
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;
}

.nav-back,
.nav-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

.nav-placeholder {
  background: transparent;
  border: none;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  pointer-events: none;
}

// 表单内容
.form-content {
  padding: 0 30rpx 200rpx;
}

.form-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
}

.card-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &.arrow:active {
    background-color: #fafafa;
  }
}

.item-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
  margin-right: 8rpx;
  font-weight: 600;
}

.item-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  gap: 10rpx;
}

.arrow .item-value {
  color: #666;
}

.amount-input {
  text-align: right;
  width: 200rpx;
  font-size: 28rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;

  &::placeholder {
    color: #999;
  }
}

.original-amount {
  color: #666;
  text-decoration: line-through;
}

.increase {
  color: #ff4d4f;
}

.decrease {
  color: #52c41a;
}

// 备注区域
.note-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.note-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.note-input-wrapper {
  position: relative;
}

.note-input {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  color: #333;
  transition: all 0.3s ease;

  &:focus {
    border-color: #fdd118;
    background-color: #fff;
  }

  &::placeholder {
    color: #999;
  }
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

// 确认按钮
.confirm-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1000;

  &.primary-btn {
    background: linear-gradient(135deg, #fdd118, #ff801c);
    border: 1px solid #fdd118;

    text {
      color: #ffffff;
      font-weight: 600;
    }

    &:active {
      background: linear-gradient(135deg, #e6c015, #e6721a);
      transform: scale(0.98);
    }
  }
}
</style>
