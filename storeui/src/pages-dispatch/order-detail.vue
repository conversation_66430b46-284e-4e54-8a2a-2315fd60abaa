<template>
  <view class="order-detail-page">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <appHead left fixed title="订单详情" @clickLeft="goBack"></appHead>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 订单详情内容 -->
    <view v-else-if="orderDetail" class="detail-content">
      <!-- 订单状态区域 -->
      <view class="status-area" :class="statusClass">
        <view class="status-text">
          {{ orderDetail.order_info.order_status_name }}
          <text v-if="shouldShowAmount">
            : ¥{{ orderDetail.order_info.total_amount || orderDetail.payment_info.pay_actual || '0' }}
          </text>
        </view>
        <view class="wallet-icon">
          <uni-icons type="wallet" size="24" color="#FFFFFF"></uni-icons>
        </view>
      </view>

      <!-- 客户联系信息 -->
      <view class="contact-info">
        <view class="phone-info">
          <text>{{ orderDetail.customer_info.customer_name || '未知客户' }} {{ formatPhone(orderDetail.customer_info.customer_mobile) }}</text>
          <view class="contact-btn" @click="contactCustomer(orderDetail.customer_info.customer_mobile)">
            <u-icon name="phone" size="16" color="#FFFFFF"></u-icon>
            <text>复制</text>
          </view>
        </view>
        <view class="address-info">
          <text>{{ orderDetail.address_info.service_address || '地址未设置' }}</text>
        </view>
      </view>

      <!-- 服务信息区块 -->
      <view class="info-block">
        <view class="block-title">服务信息</view>
        <view class="info-item" v-if="orderDetail.order_info.service_time || (orderDetail.order_info.service_date && orderDetail.order_info.service_hour)">
          <text class="item-label">服务时间</text>
          <text class="item-value">
            {{ orderDetail.order_info.service_time || formattedServiceTime }}
          </text>
        </view>
        <view class="info-item" v-if="orderDetail.remark_info.service_remark">
          <text class="item-label">服务备注</text>
          <text class="item-value">{{ orderDetail.remark_info.service_remark }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.remark_info.remark">
          <text class="item-label">客户留言</text>
          <text class="item-value">{{ orderDetail.remark_info.remark }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.address_info.address_desc">
          <text class="item-label">地址描述</text>
          <text class="item-value">{{ orderDetail.address_info.address_desc }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.raw_data && orderDetail.raw_data.source">
          <text class="item-label">订单来源</text>
          <text class="item-value">{{ orderSourceName }}</text>
        </view>
      </view>

      <!-- 项目信息区块 -->
      <view class="info-block">
        <view class="block-title">项目信息</view>
        <view class="info-item">
          <text class="item-label">项目名称</text>
          <text class="item-value">{{ orderDetail.product_info.product_name || '未知项目' }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">服务详情</text>
          <text class="item-value">
            {{ orderDetail.product_info.buy_num }}{{ orderDetail.product_info.product_unit_name || orderDetail.product_info.product_unit }} |
            {{ orderDetail.product_info.service_type_name }} |
            {{ orderDetail.product_info.product_type_name }}
          </text>
        </view>
      </view>

      <!-- 结算信息区块 -->
      <view class="info-block">
        <view class="block-title">结算信息</view>
        <view class="info-item">
          <text class="item-label">订单总金额</text>
          <text class="item-value">¥{{ orderDetail.order_info.total_amount || orderDetail.payment_info.pay_money || '0' }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.payment_info.pay_type_name && orderDetail.payment_info.pay_type_name !== '其他'">
          <text class="item-label">支付方式</text>
          <text class="item-value">{{ orderDetail.payment_info.pay_type_name }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">支付状态</text>
          <text class="item-value" :class="{ 'success': orderDetail.payment_info.pay_status === 1, 'pending': orderDetail.payment_info.pay_status === 0 || orderDetail.payment_info.pay_status === null }">
            {{ orderDetail.payment_info.pay_status_name }}
          </text>
          <!-- 待付款状态显示立即扣费按钮 -->
          <view v-if="orderDetail.payment_info.pay_status === 0 || orderDetail.payment_info.pay_status === null" class="payment-action">
            <text class="pay-now-btn" @click="handleManualPayment">立即扣费</text>
          </view>
        </view>
        <view class="info-item" v-if="orderDetail.payment_info.pay_actual">
          <text class="item-label">实付金额</text>
          <text class="item-value">¥{{ orderDetail.payment_info.pay_actual }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.payment_info.transaction_id">
          <text class="item-label">支付凭证号</text>
          <text class="item-value">{{ orderDetail.payment_info.transaction_id }}</text>
          <text class="copy-btn" @click="copyTransactionId(orderDetail.payment_info.transaction_id)">复制</text>
        </view>
      </view>

      <!-- 订单信息区块 -->
      <view class="info-block">
        <view class="block-title">订单信息</view>
        <view class="info-item">
          <text class="item-label">创建时间</text>
          <text class="item-value">{{ formatDateTime(orderDetail.order_info.create_time) }}</text>
        </view>
        <view class="info-item" v-if="orderDetail.payment_info.pay_time">
          <text class="item-label">支付时间</text>
          <text class="item-value">{{ formatDateTime(orderDetail.payment_info.pay_time) }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">订单编号</text>
          <text class="item-value">{{ orderDetail.order_info.order_number }}</text>
          <text class="copy-btn" @click="copyOrderId(orderDetail.order_info.order_number)">复制</text>
        </view>
        <view class="info-item">
          <text class="item-label">归属门店</text>
          <text class="item-value">{{ orderDetail.order_info.store_name || '未知门店' }}</text>
        </view>
      </view>

      <!-- 服务人员信息区块 -->
      <view class="info-block" v-if="orderDetail.staff_info && orderDetail.staff_info.length > 0">
        <view class="block-title">服务人员</view>
        <view class="staff-list">
          <view class="staff-item" v-for="(staff, index) in orderDetail.staff_info" :key="index">
            <view class="staff-info">
              <text class="staff-name">{{ staff.real_name || staff.user_name || '未知员工' }}</text>
              <text class="staff-mobile" v-if="staff.mobile">{{ staff.mobile }}</text>
            </view>
            <view class="staff-commission" v-if="staff.service_personal_commission">
              <text>佣金: ¥{{ staff.service_personal_commission }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息区块 -->
      <view class="info-block" v-if="orderDetail.remark_info.after_sale_remark">
        <view class="block-title">售后备注</view>
        <view class="remark-content">
          <text>{{ orderDetail.remark_info.after_sale_remark }}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <u-icon name="file-text" size="60" color="#ccc"></u-icon>
      <text class="empty-text">订单详情加载失败</text>
      <view class="retry-btn" @click="loadOrderDetail">
        <text>重新加载</text>
      </view>
    </view>

    <!-- 底部操作按钮 - 暂时隐藏 -->
    <!-- <view class="bottom-actions" style="display: none;">
      <view class="action-icon" @click="goToOrderInfo">
        <image src="/static/icons/order-info.svg" mode="aspectFit" class="action-image"></image>
        <text>编辑订单</text>
      </view>
      <view class="action-icon" @click="goToOrderList">
        <image src="/static/icons/buy-order.svg" mode="aspectFit" class="action-image"></image>
        <text>关闭订单</text>
      </view>
      <view class="action-icon" @click="goToReceive">
        <image src="/static/icons/cashier.svg" mode="aspectFit" class="action-image"></image>
        <text>收银</text>
      </view>
      <view class="action-icon" @click="goToService">
        <image src="/static/icons/dispatch.svg" mode="aspectFit" class="action-image"></image>
        <text>改价</text>
      </view>
    </view> -->
  </view>
</template>

<script>
import { get } from '@/utlis/require.js'

export default {
  components: {
    'uni-icons': () => import('@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue')
  },
  data() {
    return {
      loading: false,
      orderDetail: null,
      orderNumber: ''
    };
  },

  computed: {
    statusClass() {
      if (!this.orderDetail || !this.orderDetail.order_info) {
        return 'status-default';
      }

      const status = String(this.orderDetail.order_info.order_status);

      // 已完成、已评价
      if (status === '80' || status === '90' || status === '4') {
        return 'status-completed';
      }
      // 待支付、已接单
      else if (status === '0' || status === '10') {
        return 'status-waiting';
      }
      // 服务中相关状态
      else if (status === '50' || status === '60' || status === '70' || status === '6' || status === '7') {
        return 'status-serving';
      }
      // 派单相关状态
      else if (status === '20' || status === '40' || status === '2' || status === '3') {
        return 'status-dispatching';
      }
      // 已取消、拒绝接单
      else if (status === '99' || status === '30' || status === '5') {
        return 'status-cancelled';
      }
      // 已支付
      else if (status === '1') {
        return 'status-paid';
      }

      return 'status-default';
    },

    shouldShowAmount() {
      if (!this.orderDetail || !this.orderDetail.order_info) {
        return false;
      }

      const status = String(this.orderDetail.order_info.order_status);
      return status === '0' || status === '10';
    },

    formattedServiceTime() {
      if (!this.orderDetail || !this.orderDetail.order_info) {
        return '';
      }

      const serviceDate = this.orderDetail.order_info.service_date;
      const serviceHour = this.orderDetail.order_info.service_hour;

      if (!serviceDate) return '';

      try {
        // 提取日期部分
        const dateStr = serviceDate.split('T')[0];

        // 组合日期和时间
        if (serviceHour) {
          return `${dateStr} ${serviceHour}`;
        } else {
          return dateStr;
        }
      } catch (error) {
        return serviceDate;
      }
    },

    orderSourceName() {
      if (!this.orderDetail || !this.orderDetail.raw_data || !this.orderDetail.raw_data.source) {
        return '未知';
      }

      const source = this.orderDetail.raw_data.source;
      const sourceMap = {
        'proxy': '代客下单',
        'client': '门店端',
        'admin': '管理后台',
        'staff': '服务人员',
        'wechat': '微信小程序',
        'app': '手机应用'
      };
      return sourceMap[source] || source || '未知';
    }
  },
  onLoad(options) {
    if (options.order_number) {
      this.orderNumber = options.order_number;
      this.loadOrderDetail();
    } else {
      uni.showToast({
        title: '订单号不能为空',
        icon: 'error'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    async loadOrderDetail() {
      if (!this.orderNumber) {
        uni.showToast({
          title: '订单号不能为空',
          icon: 'error'
        });
        return;
      }

      this.loading = true;
      try {
        const response = await get('/api/v1/order/findServeOrderDetail', {
          order_number: this.orderNumber
        });

        // get方法已经处理了业务逻辑，直接使用返回的数据
        this.orderDetail = response;
        console.log('订单详情:', this.orderDetail);
      } catch (error) {
        console.error('获取订单详情失败:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'error'
        });
      } finally {
        this.loading = false;
      }
    },

    contactCustomer(phone) {
      if (!phone) {
        uni.showToast({
          title: '手机号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: phone,
        success: () => {
          uni.showToast({
            title: '电话已复制',
            icon: 'success',
          });
        },
      });
    },

    copyOrderId(orderNumber) {
      if (!orderNumber) {
        uni.showToast({
          title: '订单号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: orderNumber,
        success: () => {
          uni.showToast({
            title: '订单编号已复制',
            icon: 'success',
          });
        },
      });
    },

    copyTransactionId(transactionId) {
      if (!transactionId) {
        uni.showToast({
          title: '支付凭证号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: transactionId,
        success: () => {
          uni.showToast({
            title: '支付凭证号已复制',
            icon: 'success',
          });
        },
      });
    },

    async handleManualPayment() {
      if (!this.orderDetail || !this.orderDetail.order_info.order_number) {
        uni.showToast({
          title: '订单信息异常',
          icon: 'none'
        });
        return;
      }

      // 获取最新的订单金额
      const currentAmount = this.orderDetail.order_info.total_amount ||
                           this.orderDetail.payment_info.pay_actual ||
                           this.orderDetail.order_info.pay_actual || '0';

      // 显示确认对话框，显示具体扣费金额
      uni.showModal({
        title: '确认扣费',
        content: `确定要对订单 ${this.orderDetail.order_info.order_number} 进行余额扣费吗？\n\n扣费金额：¥${currentAmount}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performManualPayment();
          }
        }
      });
    },

    async performManualPayment() {
      try {
        uni.showLoading({
          title: '正在扣费...',
          mask: true
        });

        const response = await this.$post('/api/v1/order/manual-balance-payment', {
          order_number: this.orderDetail.order_info.order_number
        }, {
          contentType: 'application/json'
        });

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '扣费成功',
            icon: 'success'
          });

          // 重新加载订单详情
          setTimeout(() => {
            this.loadOrderDetail();
          }, 1500);
        }
      } catch (error) {
        console.error('手动扣费失败:', error);
        uni.hideLoading();

        let errorMessage = '扣费失败，请重试';
        if (error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    formatPhone(phone) {
      if (!phone) return '';
      if (phone.length === 11) {
        return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
      }
      return phone;
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';
      try {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        return dateTime;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
  padding-top: 0;
}

.nav-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 9999;
  background-color: #fff;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .loading-text,
  .empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }

  .retry-btn {
    margin-top: 30rpx;
    padding: 20rpx 40rpx;
    background-color: #fdd118;
    color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.detail-content {
  margin-top: calc(var(--status-bar-height) + 100rpx);
}

.status-area {
  padding: 40rpx 30rpx;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  &.status-waiting {
    background-color: #fdd118; // 待支付、已接单 - 黄色
  }

  &.status-paid {
    background-color: #00cc66; // 已支付 - 绿色
  }

  &.status-completed {
    background-color: #00cc66; // 已完成、已评价 - 绿色
  }

  &.status-serving {
    background-color: #007aff; // 服务中相关状态 - 蓝色
  }

  &.status-dispatching {
    background-color: #ff9500; // 派单相关状态 - 橙色
  }

  &.status-cancelled {
    background-color: #ff3b30; // 已取消、拒绝接单 - 红色
  }

  &.status-default {
    background-color: #999; // 默认状态 - 灰色
  }

  .status-text {
    font-size: 36rpx;
    font-weight: bold;
  }

  .wallet-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.contact-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .phone-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 28rpx;

    .contact-btn {
      display: flex;
      align-items: center;
      background-color: #fdd118;
      color: #fff;
      padding: 10rpx 20rpx;
      border-radius: 30rpx;
      font-size: 24rpx;

      text {
        margin-left: 6rpx;
      }
    }
  }

  .address-info {
    font-size: 28rpx;
    color: #666;
  }
}

.info-block {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .block-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
  }

  .info-item {
    display: flex;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    align-items: center;

    .item-label {
      color: #999;
      min-width: 180rpx;
    }

    .item-value {
      color: #333;
      flex: 1;
    }

    .no-value {
      color: #999;
    }

    .success {
      color: #00cc66;
    }

    .pending {
      color: #fdd118;
    }

    .red {
      color: #ff0000;
    }

    .copy-btn {
      color: #fdd118;
      font-size: 24rpx;
      padding: 0 10rpx;
    }

    .payment-action {
      margin-left: auto;
    }

    .pay-now-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      border: none;
    }
  }

  .staff-list {
    .staff-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .staff-info {
        flex: 1;

        .staff-name {
          font-size: 28rpx;
          color: #333;
          margin-right: 20rpx;
        }

        .staff-mobile {
          font-size: 24rpx;
          color: #666;
        }
      }

      .staff-commission {
        font-size: 24rpx;
        color: #fdd118;
      }
    }
  }

  .remark-content {
    padding: 20rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 1.5;
  }

  .record-list {
    .record-item {
      margin-bottom: 30rpx;

      .record-time {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 10rpx;
      }

      .record-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 15rpx 0;
  background-color: #fff;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
  border-top: 1rpx solid #eee;

  .action-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx;

    .action-image {
      width: 48rpx;
      height: 48rpx;
    }

    text {
      font-size: 24rpx;
      color: #333;
      margin-top: 8rpx;
    }
  }
}
</style>

